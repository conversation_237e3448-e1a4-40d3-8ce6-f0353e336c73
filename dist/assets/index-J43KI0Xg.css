*,
:before,
:after {
	--tw-border-spacing-x: 0;
	--tw-border-spacing-y: 0;
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-pan-x: ;
	--tw-pan-y: ;
	--tw-pinch-zoom: ;
	--tw-scroll-snap-strictness: proximity;
	--tw-gradient-from-position: ;
	--tw-gradient-via-position: ;
	--tw-gradient-to-position: ;
	--tw-ordinal: ;
	--tw-slashed-zero: ;
	--tw-numeric-figure: ;
	--tw-numeric-spacing: ;
	--tw-numeric-fraction: ;
	--tw-ring-inset: ;
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: rgb(59 130 246 / 0.5);
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
	--tw-blur: ;
	--tw-brightness: ;
	--tw-contrast: ;
	--tw-grayscale: ;
	--tw-hue-rotate: ;
	--tw-invert: ;
	--tw-saturate: ;
	--tw-sepia: ;
	--tw-drop-shadow: ;
	--tw-backdrop-blur: ;
	--tw-backdrop-brightness: ;
	--tw-backdrop-contrast: ;
	--tw-backdrop-grayscale: ;
	--tw-backdrop-hue-rotate: ;
	--tw-backdrop-invert: ;
	--tw-backdrop-opacity: ;
	--tw-backdrop-saturate: ;
	--tw-backdrop-sepia: ;
	--tw-contain-size: ;
	--tw-contain-layout: ;
	--tw-contain-paint: ;
	--tw-contain-style: ;
}
::backdrop {
	--tw-border-spacing-x: 0;
	--tw-border-spacing-y: 0;
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-pan-x: ;
	--tw-pan-y: ;
	--tw-pinch-zoom: ;
	--tw-scroll-snap-strictness: proximity;
	--tw-gradient-from-position: ;
	--tw-gradient-via-position: ;
	--tw-gradient-to-position: ;
	--tw-ordinal: ;
	--tw-slashed-zero: ;
	--tw-numeric-figure: ;
	--tw-numeric-spacing: ;
	--tw-numeric-fraction: ;
	--tw-ring-inset: ;
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: rgb(59 130 246 / 0.5);
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
	--tw-blur: ;
	--tw-brightness: ;
	--tw-contrast: ;
	--tw-grayscale: ;
	--tw-hue-rotate: ;
	--tw-invert: ;
	--tw-saturate: ;
	--tw-sepia: ;
	--tw-drop-shadow: ;
	--tw-backdrop-blur: ;
	--tw-backdrop-brightness: ;
	--tw-backdrop-contrast: ;
	--tw-backdrop-grayscale: ;
	--tw-backdrop-hue-rotate: ;
	--tw-backdrop-invert: ;
	--tw-backdrop-opacity: ;
	--tw-backdrop-saturate: ;
	--tw-backdrop-sepia: ;
	--tw-contain-size: ;
	--tw-contain-layout: ;
	--tw-contain-paint: ;
	--tw-contain-style: ;
}
*,
:before,
:after {
	box-sizing: border-box;
	border-width: 0;
	border-style: solid;
	border-color: #e5e7eb;
}
:before,
:after {
	--tw-content: '';
}
html,
:host {
	line-height: 1.5;
	-webkit-text-size-adjust: 100%;
	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;
	font-family:
		ui-sans-serif,
		system-ui,
		sans-serif,
		'Apple Color Emoji',
		'Segoe UI Emoji',
		Segoe UI Symbol,
		'Noto Color Emoji';
	font-feature-settings: normal;
	font-variation-settings: normal;
	-webkit-tap-highlight-color: transparent;
}
body {
	margin: 0;
	line-height: inherit;
}
hr {
	height: 0;
	color: inherit;
	border-top-width: 1px;
}
abbr:where([title]) {
	-webkit-text-decoration: underline dotted;
	text-decoration: underline dotted;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: inherit;
	font-weight: inherit;
}
a {
	color: inherit;
	text-decoration: inherit;
}
b,
strong {
	font-weight: bolder;
}
code,
kbd,
samp,
pre {
	font-family:
		ui-monospace,
		SFMono-Regular,
		Menlo,
		Monaco,
		Consolas,
		Liberation Mono,
		Courier New,
		monospace;
	font-feature-settings: normal;
	font-variation-settings: normal;
	font-size: 1em;
}
small {
	font-size: 80%;
}
sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}
sub {
	bottom: -0.25em;
}
sup {
	top: -0.5em;
}
table {
	text-indent: 0;
	border-color: inherit;
	border-collapse: collapse;
}
button,
input,
optgroup,
select,
textarea {
	font-family: inherit;
	font-feature-settings: inherit;
	font-variation-settings: inherit;
	font-size: 100%;
	font-weight: inherit;
	line-height: inherit;
	letter-spacing: inherit;
	color: inherit;
	margin: 0;
	padding: 0;
}
button,
select {
	text-transform: none;
}
button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
	-webkit-appearance: button;
	background-color: transparent;
	background-image: none;
}
:-moz-focusring {
	outline: auto;
}
:-moz-ui-invalid {
	box-shadow: none;
}
progress {
	vertical-align: baseline;
}
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}
[type='search'] {
	-webkit-appearance: textfield;
	outline-offset: -2px;
}
::-webkit-search-decoration {
	-webkit-appearance: none;
}
::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit;
}
summary {
	display: list-item;
}
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
	margin: 0;
}
fieldset {
	margin: 0;
	padding: 0;
}
legend {
	padding: 0;
}
ol,
ul,
menu {
	list-style: none;
	margin: 0;
	padding: 0;
}
dialog {
	padding: 0;
}
textarea {
	resize: vertical;
}
input::-moz-placeholder,
textarea::-moz-placeholder {
	opacity: 1;
	color: #9ca3af;
}
input::placeholder,
textarea::placeholder {
	opacity: 1;
	color: #9ca3af;
}
button,
[role='button'] {
	cursor: pointer;
}
:disabled {
	cursor: default;
}
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
	display: block;
	vertical-align: middle;
}
img,
video {
	max-width: 100%;
	height: auto;
}
[hidden]:where(:not([hidden='until-found'])) {
	display: none;
}
:root {
	--background: 222.2 84% 4.9%;
	--foreground: 210 40% 98%;
	--card: 222.2 84% 4.9%;
	--card-foreground: 210 40% 98%;
	--popover: 222.2 84% 4.9%;
	--popover-foreground: 210 40% 98%;
	--primary: 210 40% 98%;
	--primary-foreground: 222.2 84% 4.9%;
	--secondary: 217.2 32.6% 17.5%;
	--secondary-foreground: 210 40% 98%;
	--muted: 217.2 32.6% 17.5%;
	--muted-foreground: 215 20.2% 65.1%;
	--accent: 217.2 32.6% 17.5%;
	--accent-foreground: 210 40% 98%;
	--destructive: 0 62.8% 30.6%;
	--destructive-foreground: 210 40% 98%;
	--border: 217.2 32.6% 17.5%;
	--input: 217.2 32.6% 17.5%;
	--ring: 212.7 26.8% 83.9%;
	--radius: 0.5rem;
}
* {
	border-color: hsl(var(--border));
}
body {
	background-color: hsl(var(--background));
	color: hsl(var(--foreground));
}
.invisible {
	visibility: hidden;
}
.absolute {
	position: absolute;
}
.relative {
	position: relative;
}
.inset-0 {
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}
.left-0 {
	left: 0;
}
.left-2 {
	left: 0.5rem;
}
.top-full {
	top: 100%;
}
.z-10 {
	z-index: 10;
}
.z-50 {
	z-index: 50;
}
.-mx-1 {
	margin-left: -0.25rem;
	margin-right: -0.25rem;
}
.mx-auto {
	margin-left: auto;
	margin-right: auto;
}
.my-1 {
	margin-top: 0.25rem;
	margin-bottom: 0.25rem;
}
.mb-1 {
	margin-bottom: 0.25rem;
}
.mb-2 {
	margin-bottom: 0.5rem;
}
.mb-4 {
	margin-bottom: 1rem;
}
.ml-1 {
	margin-left: 0.25rem;
}
.ml-8 {
	margin-left: 2rem;
}
.mr-1 {
	margin-right: 0.25rem;
}
.mr-2 {
	margin-right: 0.5rem;
}
.mt-0\.5 {
	margin-top: 0.125rem;
}
.mt-1 {
	margin-top: 0.25rem;
}
.mt-12 {
	margin-top: 3rem;
}
.mt-2 {
	margin-top: 0.5rem;
}
.mt-4 {
	margin-top: 1rem;
}
.box-border {
	box-sizing: border-box;
}
.flex {
	display: flex;
}
.inline-flex {
	display: inline-flex;
}
.table {
	display: table;
}
.grid {
	display: grid;
}
.h-10 {
	height: 2.5rem;
}
.h-11 {
	height: 2.75rem;
}
.h-12 {
	height: 3rem;
}
.h-3 {
	height: 0.75rem;
}
.h-3\.5 {
	height: 0.875rem;
}
.h-4 {
	height: 1rem;
}
.h-6 {
	height: 1.5rem;
}
.h-9 {
	height: 2.25rem;
}
.h-\[var\(--radix-select-trigger-height\)\] {
	height: var(--radix-select-trigger-height);
}
.h-full {
	height: 100%;
}
.h-px {
	height: 1px;
}
.max-h-96 {
	max-height: 24rem;
}
.min-h-\[80px\] {
	min-height: 80px;
}
.min-h-screen {
	min-height: 100vh;
}
.w-10 {
	width: 2.5rem;
}
.w-3 {
	width: 0.75rem;
}
.w-3\.5 {
	width: 0.875rem;
}
.w-4 {
	width: 1rem;
}
.w-6 {
	width: 1.5rem;
}
.w-8 {
	width: 2rem;
}
.w-full {
	width: 100%;
}
.min-w-48 {
	min-width: 12rem;
}
.min-w-\[60rem\] {
	min-width: 60rem;
}
.min-w-\[8rem\] {
	min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
	min-width: var(--radix-select-trigger-width);
}
.max-w-2xl {
	max-width: 42rem;
}
.max-w-6xl {
	max-width: 72rem;
}
.max-w-sm {
	max-width: 24rem;
}
.flex-1 {
	flex: 1 1 0%;
}
.flex-none {
	flex: none;
}
.table-fixed {
	table-layout: fixed;
}
.caption-bottom {
	caption-side: bottom;
}
.translate-y-1 {
	--tw-translate-y: 0.25rem;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
		skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
		scaleY(var(--tw-scale-y));
}
.rotate-45 {
	--tw-rotate: 45deg;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
		skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
		scaleY(var(--tw-scale-y));
}
.transform {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
		skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
		scaleY(var(--tw-scale-y));
}
@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}
.animate-spin {
	animation: spin 1s linear infinite;
}
.cursor-default {
	cursor: default;
}
.cursor-pointer {
	cursor: pointer;
}
.select-none {
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;
}
.list-disc {
	list-style-type: disc;
}
.grid-cols-1 {
	grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
	grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-\[1fr_10fr_1fr\] {
	grid-template-columns: 1fr 10fr 1fr;
}
.grid-rows-2 {
	grid-template-rows: repeat(2, minmax(0, 1fr));
}
.flex-col {
	flex-direction: column;
}
.items-start {
	align-items: flex-start;
}
.items-center {
	align-items: center;
}
.justify-center {
	justify-content: center;
}
.justify-between {
	justify-content: space-between;
}
.gap-1 {
	gap: 0.25rem;
}
.gap-2 {
	gap: 0.5rem;
}
.gap-4 {
	gap: 1rem;
}
.gap-8 {
	gap: 2rem;
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.overflow-auto {
	overflow: auto;
}
.overflow-hidden {
	overflow: hidden;
}
.overflow-x-auto {
	overflow-x: auto;
}
.truncate {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.whitespace-nowrap {
	white-space: nowrap;
}
.rounded {
	border-radius: 0.25rem;
}
.rounded-full {
	border-radius: 9999px;
}
.rounded-lg {
	border-radius: var(--radius);
}
.rounded-md {
	border-radius: calc(var(--radius) - 2px);
}
.rounded-sm {
	border-radius: calc(var(--radius) - 4px);
}
.border {
	border-width: 1px;
}
.border-b {
	border-bottom-width: 1px;
}
.border-t {
	border-top-width: 1px;
}
.border-border {
	border-color: hsl(var(--border));
}
.border-destructive\/50 {
	border-color: hsl(var(--destructive) / 0.5);
}
.border-input {
	border-color: hsl(var(--input));
}
.bg-background {
	background-color: hsl(var(--background));
}
.bg-blue-100 {
	--tw-bg-opacity: 1;
	background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-card {
	background-color: hsl(var(--card));
}
.bg-destructive {
	background-color: hsl(var(--destructive));
}
.bg-green-100 {
	--tw-bg-opacity: 1;
	background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-muted {
	background-color: hsl(var(--muted));
}
.bg-muted\/50 {
	background-color: hsl(var(--muted) / 0.5);
}
.bg-popover {
	background-color: hsl(var(--popover));
}
.bg-primary {
	background-color: hsl(var(--primary));
}
.bg-secondary {
	background-color: hsl(var(--secondary));
}
.p-1 {
	padding: 0.25rem;
}
.p-2 {
	padding: 0.5rem;
}
.p-3 {
	padding: 0.75rem;
}
.p-4 {
	padding: 1rem;
}
.p-6 {
	padding: 1.5rem;
}
.px-2 {
	padding-left: 0.5rem;
	padding-right: 0.5rem;
}
.px-3 {
	padding-left: 0.75rem;
	padding-right: 0.75rem;
}
.px-4 {
	padding-left: 1rem;
	padding-right: 1rem;
}
.px-8 {
	padding-left: 2rem;
	padding-right: 2rem;
}
.py-1 {
	padding-top: 0.25rem;
	padding-bottom: 0.25rem;
}
.py-1\.5 {
	padding-top: 0.375rem;
	padding-bottom: 0.375rem;
}
.py-2 {
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
}
.py-8 {
	padding-top: 2rem;
	padding-bottom: 2rem;
}
.pl-8 {
	padding-left: 2rem;
}
.pr-2 {
	padding-right: 0.5rem;
}
.pt-0 {
	padding-top: 0;
}
.text-left {
	text-align: left;
}
.text-center {
	text-align: center;
}
.align-middle {
	vertical-align: middle;
}
.text-2xl {
	font-size: 1.5rem;
	line-height: 2rem;
}
.text-3xl {
	font-size: 1.875rem;
	line-height: 2.25rem;
}
.text-lg {
	font-size: 1.125rem;
	line-height: 1.75rem;
}
.text-sm {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
.text-xs {
	font-size: 0.75rem;
	line-height: 1rem;
}
.font-medium {
	font-weight: 500;
}
.font-semibold {
	font-weight: 600;
}
.leading-none {
	line-height: 1;
}
.tracking-tight {
	letter-spacing: -0.025em;
}
.text-blue-900 {
	--tw-text-opacity: 1;
	color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-card-foreground {
	color: hsl(var(--card-foreground));
}
.text-destructive {
	color: hsl(var(--destructive));
}
.text-destructive-foreground {
	color: hsl(var(--destructive-foreground));
}
.text-foreground {
	color: hsl(var(--foreground));
}
.text-green-900 {
	--tw-text-opacity: 1;
	color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}
.text-muted-foreground {
	color: hsl(var(--muted-foreground));
}
.text-popover-foreground {
	color: hsl(var(--popover-foreground));
}
.text-primary {
	color: hsl(var(--primary));
}
.text-primary-foreground {
	color: hsl(var(--primary-foreground));
}
.text-secondary-foreground {
	color: hsl(var(--secondary-foreground));
}
.underline {
	text-decoration-line: underline;
}
.underline-offset-4 {
	text-underline-offset: 4px;
}
.opacity-50 {
	opacity: 0.5;
}
.shadow-lg {
	--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
	--tw-shadow-colored:
		0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
	box-shadow:
		var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
	--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
	box-shadow:
		var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
	--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
	box-shadow:
		var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.outline {
	outline-style: solid;
}
.ring-offset-background {
	--tw-ring-offset-color: hsl(var(--background));
}
.transition-colors {
	transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 0.15s;
}
@keyframes enter {
	0% {
		opacity: var(--tw-enter-opacity, 1);
		transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0)
			scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1))
			rotate(var(--tw-enter-rotate, 0));
	}
}
@keyframes exit {
	to {
		opacity: var(--tw-exit-opacity, 1);
		transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0)
			scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1))
			rotate(var(--tw-exit-rotate, 0));
	}
}
.file\:border-0::file-selector-button {
	border-width: 0px;
}
.file\:bg-transparent::file-selector-button {
	background-color: transparent;
}
.file\:text-sm::file-selector-button {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
.file\:font-medium::file-selector-button {
	font-weight: 500;
}
.placeholder\:text-muted-foreground::-moz-placeholder {
	color: hsl(var(--muted-foreground));
}
.placeholder\:text-muted-foreground::placeholder {
	color: hsl(var(--muted-foreground));
}
.hover\:bg-accent:hover {
	background-color: hsl(var(--accent));
}
.hover\:bg-blue-200:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-destructive\/90:hover {
	background-color: hsl(var(--destructive) / 0.9);
}
.hover\:bg-muted\/50:hover {
	background-color: hsl(var(--muted) / 0.5);
}
.hover\:bg-primary\/90:hover {
	background-color: hsl(var(--primary) / 0.9);
}
.hover\:bg-secondary\/80:hover {
	background-color: hsl(var(--secondary) / 0.8);
}
.hover\:text-accent-foreground:hover {
	color: hsl(var(--accent-foreground));
}
.hover\:underline:hover {
	text-decoration-line: underline;
}
.focus\:bg-accent:focus {
	background-color: hsl(var(--accent));
}
.focus\:text-accent-foreground:focus {
	color: hsl(var(--accent-foreground));
}
.focus\:outline-none:focus {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.focus\:ring-2:focus {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
		var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
		var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-ring:focus {
	--tw-ring-color: hsl(var(--ring));
}
.focus\:ring-offset-2:focus {
	--tw-ring-offset-width: 2px;
}
.focus-visible\:outline-none:focus-visible {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.focus-visible\:ring-2:focus-visible {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
		var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
		var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-ring:focus-visible {
	--tw-ring-color: hsl(var(--ring));
}
.focus-visible\:ring-offset-2:focus-visible {
	--tw-ring-offset-width: 2px;
}
.disabled\:pointer-events-none:disabled {
	pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
	cursor: not-allowed;
}
.disabled\:opacity-50:disabled {
	opacity: 0.5;
}
.group:hover .group-hover\:visible {
	visibility: visible;
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
	cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-70 {
	opacity: 0.7;
}
.data-\[disabled\]\:pointer-events-none[data-disabled] {
	pointer-events: none;
}
.data-\[side\=bottom\]\:translate-y-1[data-side='bottom'] {
	--tw-translate-y: 0.25rem;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
		skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
		scaleY(var(--tw-scale-y));
}
.data-\[side\=left\]\:-translate-x-1[data-side='left'] {
	--tw-translate-x: -0.25rem;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
		skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
		scaleY(var(--tw-scale-y));
}
.data-\[side\=right\]\:translate-x-1[data-side='right'] {
	--tw-translate-x: 0.25rem;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
		skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
		scaleY(var(--tw-scale-y));
}
.data-\[side\=top\]\:-translate-y-1[data-side='top'] {
	--tw-translate-y: -0.25rem;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
		skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
		scaleY(var(--tw-scale-y));
}
.data-\[state\=selected\]\:bg-muted[data-state='selected'] {
	background-color: hsl(var(--muted));
}
.data-\[disabled\]\:opacity-50[data-disabled] {
	opacity: 0.5;
}
.data-\[state\=open\]\:animate-in[data-state='open'] {
	animation-name: enter;
	animation-duration: 0.15s;
	--tw-enter-opacity: initial;
	--tw-enter-scale: initial;
	--tw-enter-rotate: initial;
	--tw-enter-translate-x: initial;
	--tw-enter-translate-y: initial;
}
.data-\[state\=closed\]\:animate-out[data-state='closed'] {
	animation-name: exit;
	animation-duration: 0.15s;
	--tw-exit-opacity: initial;
	--tw-exit-scale: initial;
	--tw-exit-rotate: initial;
	--tw-exit-translate-x: initial;
	--tw-exit-translate-y: initial;
}
.data-\[state\=closed\]\:fade-out-0[data-state='closed'] {
	--tw-exit-opacity: 0;
}
.data-\[state\=open\]\:fade-in-0[data-state='open'] {
	--tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state='closed'] {
	--tw-exit-scale: 0.95;
}
.data-\[state\=open\]\:zoom-in-95[data-state='open'] {
	--tw-enter-scale: 0.95;
}
.data-\[side\=bottom\]\:slide-in-from-top-2[data-side='bottom'] {
	--tw-enter-translate-y: -0.5rem;
}
.data-\[side\=left\]\:slide-in-from-right-2[data-side='left'] {
	--tw-enter-translate-x: 0.5rem;
}
.data-\[side\=right\]\:slide-in-from-left-2[data-side='right'] {
	--tw-enter-translate-x: -0.5rem;
}
.data-\[side\=top\]\:slide-in-from-bottom-2[data-side='top'] {
	--tw-enter-translate-y: 0.5rem;
}
.dark\:border-destructive:is(.dark *) {
	border-color: hsl(var(--destructive));
}
.dark\:bg-blue-900:is(.dark *) {
	--tw-bg-opacity: 1;
	background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
.dark\:bg-green-900:is(.dark *) {
	--tw-bg-opacity: 1;
	background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}
.dark\:text-blue-100:is(.dark *) {
	--tw-text-opacity: 1;
	color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.dark\:text-green-100:is(.dark *) {
	--tw-text-opacity: 1;
	color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}
.dark\:hover\:bg-blue-800:hover:is(.dark *) {
	--tw-bg-opacity: 1;
	background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
@media (min-width: 1024px) {
	.lg\:grid-cols-2 {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
}
.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role='checkbox']) {
	padding-right: 0;
}
.\[\&\>span\]\:line-clamp-1 > span {
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
}
.\[\&\>svg\+div\]\:translate-y-\[-3px\] > svg + div {
	--tw-translate-y: -3px;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
		skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
		scaleY(var(--tw-scale-y));
}
.\[\&\>svg\]\:absolute > svg {
	position: absolute;
}
.\[\&\>svg\]\:left-4 > svg {
	left: 1rem;
}
.\[\&\>svg\]\:top-4 > svg {
	top: 1rem;
}
.\[\&\>svg\]\:text-destructive > svg {
	color: hsl(var(--destructive));
}
.\[\&\>svg\]\:text-foreground > svg {
	color: hsl(var(--foreground));
}
.\[\&\>svg\~\*\]\:pl-7 > svg ~ * {
	padding-left: 1.75rem;
}
.\[\&\>tr\]\:last\:border-b-0:last-child > tr {
	border-bottom-width: 0px;
}
.\[\&_p\]\:leading-relaxed p {
	line-height: 1.625;
}
.\[\&_tr\:last-child\]\:border-0 tr:last-child {
	border-width: 0px;
}
.\[\&_tr\]\:border-b tr {
	border-bottom-width: 1px;
}
