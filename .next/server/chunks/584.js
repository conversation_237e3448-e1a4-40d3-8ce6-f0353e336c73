exports.id=584,exports.ids=[584],exports.modules={1794:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},169:(e,t,r)=>{Promise.resolve().then(r.bind(r,3809)),Promise.resolve().then(r.bind(r,5008))},3809:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var s=r(2295),n=r(3729),a=r.n(n),o=r(2254),i=r(5008),d=r(339),u=r(2437),l=r(9247),c=r(4513),f=r(1453);let p=u.zt,m=n.forwardRef(({className:e,...t},r)=>s.jsx(u.l_,{ref:r,className:(0,f.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));m.displayName=u.l_.displayName;let h=(0,l.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),x=n.forwardRef(({className:e,variant:t,...r},n)=>s.jsx(u.fC,{ref:n,className:(0,f.cn)(h({variant:t}),e),...r}));x.displayName=u.fC.displayName,n.forwardRef(({className:e,...t},r)=>s.jsx(u.aU,{ref:r,className:(0,f.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=u.aU.displayName;let g=n.forwardRef(({className:e,...t},r)=>s.jsx(u.x8,{ref:r,className:(0,f.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:s.jsx(c.Z,{className:"h-4 w-4"})}));g.displayName=u.x8.displayName;let v=n.forwardRef(({className:e,...t},r)=>s.jsx(u.Dx,{ref:r,className:(0,f.cn)("text-sm font-semibold",e),...t}));v.displayName=u.Dx.displayName;let y=n.forwardRef(({className:e,...t},r)=>s.jsx(u.dk,{ref:r,className:(0,f.cn)("text-sm opacity-90",e),...t}));function b(){let{toasts:e}=(0,d.pm)();return(0,s.jsxs)(p,{children:[e.map(function({id:e,title:t,description:r,action:n,...a}){return(0,s.jsxs)(x,{...a,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&s.jsx(v,{children:t}),r&&s.jsx(y,{children:r})]}),n,s.jsx(g,{})]},e)}),s.jsx(m,{})]})}y.displayName=u.dk.displayName;var T=r(5961),A=r(3733),S=r(5094),w=r(3673);class N extends a().Component{constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error caught by boundary:",e,t)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return s.jsx(e,{error:this.state.error,resetError:this.resetError})}return s.jsx(E,{error:this.state.error,resetError:this.resetError})}return this.props.children}}function E({error:e,resetError:t}){return s.jsx("div",{className:"flex items-center justify-center min-h-[400px] p-4",children:(0,s.jsxs)(w.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(w.Ol,{className:"text-center",children:[s.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10",children:s.jsx(T.Z,{className:"h-6 w-6 text-destructive"})}),s.jsx(w.ll,{children:"C\xf3 lỗi xảy ra"}),s.jsx(w.SZ,{children:"Ứng dụng đ\xe3 gặp phải một lỗi kh\xf4ng mong muốn."})]}),(0,s.jsxs)(w.aY,{className:"space-y-4",children:[(0,s.jsxs)("details",{className:"text-sm",children:[s.jsx("summary",{className:"cursor-pointer text-muted-foreground hover:text-foreground",children:"Chi tiết lỗi"}),s.jsx("pre",{className:"mt-2 whitespace-pre-wrap break-words text-xs bg-muted p-2 rounded",children:e.message})]}),(0,s.jsxs)(S.z,{onClick:t,className:"w-full",children:[s.jsx(A.Z,{className:"mr-2 h-4 w-4"}),"Thử lại"]})]})]})})}function j(){return s.jsx(S.z,{onClick:()=>{let e=document.querySelector("main");e&&(e.focus(),e.scrollIntoView())},className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50",variant:"outline",children:"Skip to main content"})}function _({children:e}){let{isAuthenticated:t,isLoading:r}=(0,i.useAuth)(),a=(0,o.useRouter)(),d=(0,o.usePathname)();return((0,n.useEffect)(()=>{if(!r){let e="/login"===d,r="/"===d||"/about"===d||"/changelogs"===d;t||e||r?t&&e&&a.push("/calendar"):a.push("/login")}},[t,r,d,a]),r)?s.jsx("div",{className:"min-h-screen flex items-center justify-center",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):(0,s.jsxs)(N,{children:[s.jsx(j,{}),(0,s.jsxs)("div",{className:(0,f.cn)("min-h-screen bg-background text-foreground","flex flex-col"),children:[e,s.jsx(b,{})]})]})}},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>u});var s=r(2295),n=r(3729),a=r(2751),o=r(9247),i=r(1453);let d=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...o},u)=>{let l=n?a.g7:"button";return s.jsx(l,{className:(0,i.cn)(d({variant:t,size:r,className:e})),ref:u,...o})});u.displayName="Button"},3673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>i,SZ:()=>u,Zb:()=>o,aY:()=>l,ll:()=>d});var s=r(2295),n=r(3729),a=r(1453);let o=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let i=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let d=n.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let u=n.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));u.displayName="CardDescription";let l=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));l.displayName="CardContent",n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},5008:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AppProvider:()=>u,useApp:()=>l,useAuth:()=>c,useCalendar:()=>f,useUI:()=>p});var s=r(2295),n=r(3729),a=r(6980);let o={auth:{user:null,isAuthenticated:!1,isLoading:!1,error:null},calendar:null,ui:{theme:"dark",sidebarOpen:!1,currentView:"calendar"},student:null};function i(e,t){switch(t.type){case"AUTH_START":return{...e,auth:{...e.auth,isLoading:!0,error:null}};case"AUTH_SUCCESS":return{...e,auth:{user:t.payload.user,isAuthenticated:!0,isLoading:!1,error:null}};case"AUTH_ERROR":return{...e,auth:{user:null,isAuthenticated:!1,isLoading:!1,error:t.payload}};case"AUTH_LOGOUT":return{...e,auth:{user:null,isAuthenticated:!1,isLoading:!1,error:null},calendar:null,student:null};case"SET_CALENDAR":return{...e,calendar:t.payload};case"SET_STUDENT":return{...e,student:t.payload};case"SET_THEME":return{...e,ui:{...e.ui,theme:t.payload}};case"TOGGLE_SIDEBAR":return{...e,ui:{...e.ui,sidebarOpen:!e.ui.sidebarOpen}};case"SET_VIEW":return{...e,ui:{...e.ui,currentView:t.payload}};case"LOAD_FROM_STORAGE":let{signInToken:r,calendar:s,student:n,user:a}=t.payload;return{...e,auth:{user:a||null,isAuthenticated:!!(r||s),isLoading:!1,error:null},calendar:s||null,student:n||null};default:return e}}let d=(0,n.createContext)(null);function u({children:e}){let[t,r]=(0,n.useReducer)(i,o);return(0,n.useEffect)(()=>{let e=(0,a.mu)();e&&r({type:"LOAD_FROM_STORAGE",payload:e})},[]),(0,n.useEffect)(()=>{if(t.auth.isAuthenticated&&t.calendar){let e={calendar:t.calendar,student:t.student||void 0,user:t.auth.user||void 0};(0,a.OH)(e)}},[t.auth.isAuthenticated,t.calendar,t.student,t.auth.user]),s.jsx(d.Provider,{value:{state:t,dispatch:r},children:e})}function l(){let e=(0,n.useContext)(d);if(!e)throw Error("useApp must be used within an AppProvider");return e}function c(){let{state:e,dispatch:t}=l();return{...e.auth,login:(e,r)=>t({type:"AUTH_SUCCESS",payload:{user:e,signInToken:r}}),logout:()=>t({type:"AUTH_LOGOUT"}),setLoading:()=>t({type:"AUTH_START"}),setError:e=>t({type:"AUTH_ERROR",payload:e})}}function f(){let{state:e,dispatch:t}=l();return{calendar:e.calendar,student:e.student,setCalendar:e=>t({type:"SET_CALENDAR",payload:e}),setStudent:e=>t({type:"SET_STUDENT",payload:e})}}function p(){let{state:e,dispatch:t}=l();return{...e.ui,setTheme:e=>t({type:"SET_THEME",payload:e}),toggleSidebar:()=>t({type:"TOGGLE_SIDEBAR"}),setView:e=>t({type:"SET_VIEW",payload:e})}}},339:(e,t,r)=>{"use strict";r.d(t,{pm:()=>f});var s=r(3729);let n=0,a=new Map,o=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],u={toasts:[]};function l(e){u=i(u,e),d.forEach(e=>{e(u)})}function c({...e}){let t=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>l({type:"DISMISS_TOAST",toastId:t});return l({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=s.useState(u);return s.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},6980:(e,t,r)=>{"use strict";function s(e){}function n(){return{calendar:null,student:null,semesters:null,mainForm:null,signInToken:null}}function a(){}r.d(t,{Nk:()=>a,OH:()=>s,mu:()=>n})},1453:(e,t,r)=>{"use strict";r.d(t,{N8:()=>l,UZ:()=>f,cn:()=>i,e$:()=>u,kJ:()=>c,p6:()=>d});var s=r(6815),n=r(9377),a=r(7030),o=r.n(a);function i(...e){return(0,n.m6)((0,s.W)(e))}function d(e,t="DD/MM/YYYY"){return o()(e).format(t)}function u(e){return e instanceof Error?e.message:"string"==typeof e?e:"Đ\xe3 xảy ra lỗi kh\xf4ng x\xe1c định"}function l(e){return({1:{start:"07:00",end:"07:50"},2:{start:"08:00",end:"08:50"},3:{start:"09:00",end:"09:50"},4:{start:"10:00",end:"10:50"},5:{start:"11:00",end:"11:50"},6:{start:"12:00",end:"12:50"},7:{start:"13:00",end:"13:50"},8:{start:"14:00",end:"14:50"},9:{start:"15:00",end:"15:50"},10:{start:"16:00",end:"16:50"},11:{start:"17:00",end:"17:50"},12:{start:"18:00",end:"18:50"},13:{start:"19:00",end:"19:50"},14:{start:"20:00",end:"20:50"},15:{start:"21:00",end:"21:50"}})[e]||{start:"00:00",end:"00:00"}}function c(e){return e>=1&&e<=6?"morning":e>=7&&e<=12?"afternoon":"evening"}function f(e){return["Chủ nhật","Thứ hai","Thứ ba","Thứ tư","Thứ năm","Thứ s\xe1u","Thứ bảy"][e]||"Kh\xf4ng x\xe1c định"}},2555:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>p});var s=r(5036);r(5023);var n=r(6843);let a=(0,n.createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx`),{__esModule:o,$$typeof:i}=a;a.default;let d=(0,n.createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx#AppProvider`);(0,n.createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx#useApp`),(0,n.createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx#useAuth`),(0,n.createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx#useCalendar`),(0,n.createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx#useUI`);let u=(0,n.createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/AppLayout.tsx`),{__esModule:l,$$typeof:c}=u,f=u.default,p={title:"KMA Schedule",description:"KMA Schedule - View your class schedule"};function m({children:e}){return s.jsx("html",{lang:"en",className:"dark",children:s.jsx("body",{children:s.jsx(d,{children:s.jsx(f,{children:e})})})})}},5023:()=>{}};