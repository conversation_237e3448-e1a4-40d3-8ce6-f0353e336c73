<!DOCTYPE html><html lang="en" class="dark"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/47bd493aef2edd18.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-fe6228ff5da2186b.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-ffbed56dc0a933ef.js" async="" crossorigin=""></script><script src="/_next/static/chunks/938-0ac063a56f3acb4b.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-0c6e7f9cab23e9d9.js" async="" crossorigin=""></script><script src="/_next/static/chunks/13b76428-56ef92d027d6786a.js" async=""></script><script src="/_next/static/chunks/666-5bfa10bdfdf7ee40.js" async=""></script><script src="/_next/static/chunks/485-e1e61c2e882eb88e.js" async=""></script><script src="/_next/static/chunks/app/(main)/layout-229a15bfd4c0f7cb.js" async=""></script><script src="/_next/static/chunks/213-e97c2b38e8417ef7.js" async=""></script><script src="/_next/static/chunks/515-bdf74ec7b0728888.js" async=""></script><script src="/_next/static/chunks/710-11e71bc3ff2744b7.js" async=""></script><script src="/_next/static/chunks/app/layout-6f348fdcf983af0c.js" async=""></script><title>KMA Schedule</title><meta name="description" content="KMA Schedule - View your class schedule"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50">Skip to main content</button><div class="min-h-screen bg-background text-foreground flex flex-col"><div class="flex flex-col min-h-screen"><header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"><div class="container mx-auto px-4"><div class="flex h-16 items-center justify-between"><div class="flex items-center space-x-4"><a class="text-xl font-bold hover:text-primary transition-colors" href="/">ACTVN SCHEDULE</a></div><nav class="hidden md:flex items-center space-x-6"><a class="text-sm font-medium transition-colors hover:text-primary text-muted-foreground" href="/changelogs">Changelogs</a><a class="text-sm font-medium transition-colors hover:text-primary text-primary" href="/about">About</a><div class="h-4 w-px bg-border"></div><a href="https://www.facebook.com/kitclubKMA" target="_blank" rel="noopener noreferrer" class="text-sm font-medium text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1">KIT Club<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></a><a href="https://github.com/ngosangns/kma-schedule-ngosangns/issues" target="_blank" rel="noopener noreferrer" class="text-sm font-medium text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1">Issues<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></a></nav><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu h-5 w-5"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></header><main class="flex-1 container mx-auto px-4 py-6 max-w-7xl" tabindex="-1" role="main" aria-label="Main content"><div class="space-y-6"><div class="text-center space-y-2"><h1 class="text-3xl font-bold">Về ACTVN Schedule</h1><p class="text-muted-foreground">Ứng dụng xem thời khóa biểu dành cho sinh viên Học viện Kỹ thuật Mật mã</p></div><div class="grid gap-6 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code h-5 w-5"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg>Về dự án</h3></div><div class="p-6 pt-0 space-y-4"><p class="text-sm text-muted-foreground">ACTVN Schedule là một ứng dụng web hiện đại được xây dựng để giúp sinh viên Học viện Kỹ thuật Mật mã xem thời khóa biểu một cách dễ dàng và tiện lợi.</p><div class="space-y-2"><h4 class="font-medium">Tính năng chính:</h4><ul class="text-sm text-muted-foreground space-y-1 ml-4"><li>• Xem thời khóa biểu theo tuần</li><li>• Giao diện responsive, thân thiện với mobile</li><li>• Xuất lịch sang Google Calendar</li><li>• Lọc theo buổi học (sáng, chiều, tối)</li><li>• Chế độ xem lịch và danh sách</li><li>• Dark mode</li></ul></div><div class="flex flex-wrap gap-2"><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80">Next.js</div><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80">TypeScript</div><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80">Tailwind CSS</div><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80">shadcn/ui</div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-5 w-5"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>Đội ngũ phát triển</h3></div><div class="p-6 pt-0 space-y-4"><div class="space-y-3"><div class="flex items-center gap-3"><div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code h-5 w-5"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><div><p class="font-medium">ngosangns</p><p class="text-sm text-muted-foreground">Lead Developer</p></div></div></div><div data-orientation="horizontal" role="none" class="shrink-0 bg-border h-[1px] w-full"></div><div class="space-y-2"><h4 class="font-medium">Liên hệ:</h4><div class="space-y-2"><a href="https://github.com/ngosangns" target="_blank" rel="noopener noreferrer" class="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github h-4 w-4"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg>GitHub<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></a></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm md:col-span-2"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-smartphone h-5 w-5"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"></rect><path d="M12 18h.01"></path></svg>Các dự án liên quan</h3><p class="text-sm text-muted-foreground">Những ứng dụng khác mà chúng tôi đã phát triển cho sinh viên KMA</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2"><div class="p-4 border rounded-lg"><div class="flex items-start justify-between mb-2"><h4 class="font-medium">KIT Schedule</h4><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground">Mobile App</div></div><p class="text-sm text-muted-foreground mb-3">Ứng dụng xem lịch học trên điện thoại dành cho sinh viên học viện KMA.</p><a href="https://play.google.com/store/apps/details?id=kma.hatuan314.schedule" target="_blank" rel="noopener noreferrer" class="inline-flex items-center gap-1 text-sm text-primary hover:underline">Xem trên Google Play<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></a></div><div class="p-4 border rounded-lg"><div class="flex items-start justify-between mb-2"><h4 class="font-medium">KMA Tín chỉ</h4><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground">Web Tool</div></div><p class="text-sm text-muted-foreground mb-3">Tool hỗ trợ sinh viên sắp xếp lịch học hợp lí cho bản thân vào mỗi mùa đăng ký học.</p><a href="https://github.com/ngosangns/tin-chi" target="_blank" rel="noopener noreferrer" class="inline-flex items-center gap-1 text-sm text-primary hover:underline">Xem trên GitHub<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></a></div></div></div></div></div></div></main><footer class="border-t bg-background"><div class="container mx-auto px-4 py-6"><div class="text-center text-sm text-muted-foreground"><p>KMA Schedule v2022.12 - ngosangns</p><p class="mt-1">Built with Next.js, TypeScript, and shadcn/ui</p></div></div></footer></div><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div></div><script src="/_next/static/chunks/webpack-fe6228ff5da2186b.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/47bd493aef2edd18.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[7690,[],\"\"]\n5:I[5613,[],\"\"]\n6:I[1778,[],\"\"]\n7:I[2767,[\"990\",\"static/chunks/13b76428-56ef92d027d6786a.js\",\"666\",\"static/chunks/666-5bfa10bdfdf7ee40.js\",\"485\",\"static/chunks/485-e1e61c2e882eb88e.js\",\"95\",\"static/chunks/app/(main)/layout-229a15bfd4c0f7cb.js\"],\"\"]\n8:I[5300,[\"990\",\"static/chunks/13b76428-56ef92d027d6786a.js\",\"666\",\"static/chunks/666-5bfa10bdfdf7ee40.js\",\"213\",\"static/chunks/213-e97c2b38e8417ef7.js\",\"515\",\"static/chunks/515-bdf74ec7b0728888.js\",\"710\",\"static/chunks/710-11e71bc3ff2744b7.js\",\"1"])</script><script>self.__next_f.push([1,"85\",\"static/chunks/app/layout-6f348fdcf983af0c.js\"],\"AppProvider\"]\n9:I[5185,[\"990\",\"static/chunks/13b76428-56ef92d027d6786a.js\",\"666\",\"static/chunks/666-5bfa10bdfdf7ee40.js\",\"213\",\"static/chunks/213-e97c2b38e8417ef7.js\",\"515\",\"static/chunks/515-bdf74ec7b0728888.js\",\"710\",\"static/chunks/710-11e71bc3ff2744b7.js\",\"185\",\"static/chunks/app/layout-6f348fdcf983af0c.js\"],\"\"]\nf:I[8955,[],\"\"]\na:{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\""])</script><script>self.__next_f.push([1,"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"}\nb:{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"}\nc:{\"display\":\"inline-block\"}\nd:{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0}\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/47bd493aef2edd18.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"CUrclrRg172ixFW01NU7X\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/about\",\"initialTree\":[\"\",{\"children\":[\"(main)\",{\"children\":[\"about\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"(main)\",{\"children\":[\"about\",{\"children\":[\"__PAGE__\",{},[\"$L4\",[\"$\",\"div\",null,{\"className\":\"space-y-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-center space-y-2\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold\",\"children\":\"Về ACTVN Schedule\"}],[\"$\",\"p\",null,{\"className\":\"text-muted-foreground\",\"children\":\"Ứng dụng xem thời khóa biểu dành cho sinh viên Học viện Kỹ thuật Mật mã\"}]]}],[\"$\",\"div\",null,{\"className\":\"grid gap-6 md:grid-cols-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"rounded-lg border bg-card text-card-foreground shadow-sm\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col space-y-1.5 p-6\",\"children\":[\"$\",\"h3\",null,{\"className\":\"text-2xl font-semibold leading-none tracking-tight flex items-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-code h-5 w-5\",\"children\":[[\"$\",\"polyline\",\"z7tu5w\",{\"points\":\"16 18 22 12 16 6\"}],[\"$\",\"polyline\",\"1eg1df\",{\"points\":\"8 6 2 12 8 18\"}],\"$undefined\"]}],\"Về dự án\"]}]}],[\"$\",\"div\",null,{\"className\":\"p-6 pt-0 space-y-4\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground\",\"children\":\"ACTVN Schedule là một ứng dụng web hiện đại được xây dựng để giúp sinh viên Học viện Kỹ thuật Mật mã xem thời khóa biểu một cách dễ dàng và tiện lợi.\"}],[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-medium\",\"children\":\"Tính năng chính:\"}],[\"$\",\"ul\",null,{\"className\":\"text-sm text-muted-foreground space-y-1 ml-4\",\"children\":[[\"$\",\"li\",null,{\"children\":\"• Xem thời khóa biểu theo tuần\"}],[\"$\",\"li\",null,{\"children\":\"• Giao diện responsive, thân thiện với mobile\"}],[\"$\",\"li\",null,{\"children\":\"• Xuất lịch sang Google Calendar\"}],[\"$\",\"li\",null,{\"children\":\"• Lọc theo buổi học (sáng, chiều, tối)\"}],[\"$\",\"li\",null,{\"children\":\"• Chế độ xem lịch và danh sách\"}],[\"$\",\"li\",null,{\"children\":\"• Dark mode\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\"children\":\"Next.js\"}],[\"$\",\"div\",null,{\"className\":\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\"children\":\"TypeScript\"}],[\"$\",\"div\",null,{\"className\":\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\"children\":\"Tailwind CSS\"}],[\"$\",\"div\",null,{\"className\":\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\"children\":\"shadcn/ui\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"rounded-lg border bg-card text-card-foreground shadow-sm\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col space-y-1.5 p-6\",\"children\":[\"$\",\"h3\",null,{\"className\":\"text-2xl font-semibold leading-none tracking-tight flex items-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-users h-5 w-5\",\"children\":[[\"$\",\"path\",\"1yyitq\",{\"d\":\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"}],[\"$\",\"circle\",\"nufk8\",{\"cx\":\"9\",\"cy\":\"7\",\"r\":\"4\"}],[\"$\",\"path\",\"kshegd\",{\"d\":\"M22 21v-2a4 4 0 0 0-3-3.87\"}],[\"$\",\"path\",\"1da9ce\",{\"d\":\"M16 3.13a4 4 0 0 1 0 7.75\"}],\"$undefined\"]}],\"Đội ngũ phát triển\"]}]}],[\"$\",\"div\",null,{\"className\":\"p-6 pt-0 space-y-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-3\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center gap-3\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-code h-5 w-5\",\"children\":[[\"$\",\"polyline\",\"z7tu5w\",{\"points\":\"16 18 22 12 16 6\"}],[\"$\",\"polyline\",\"1eg1df\",{\"points\":\"8 6 2 12 8 18\"}],\"$undefined\"]}]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"p\",null,{\"className\":\"font-medium\",\"children\":\"ngosangns\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground\",\"children\":\"Lead Developer\"}]]}]]}]}],[\"$\",\"div\",null,{\"data-orientation\":\"horizontal\",\"role\":\"none\",\"className\":\"shrink-0 bg-border h-[1px] w-full\"}],[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-medium\",\"children\":\"Liên hệ:\"}],[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/ngosangns\",\"target\":\"_blank\",\"rel\":\"noopener noreferrer\",\"className\":\"flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-github h-4 w-4\",\"children\":[[\"$\",\"path\",\"tonef\",{\"d\":\"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4\"}],[\"$\",\"path\",\"9comsn\",{\"d\":\"M9 18c-4.51 2-5-2-7-2\"}],\"$undefined\"]}],\"GitHub\",[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link h-3 w-3\",\"children\":[[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],[\"$\",\"polyline\",\"mznyad\",{\"points\":\"15 3 21 3 21 9\"}],[\"$\",\"line\",\"18c3s4\",{\"x1\":\"10\",\"x2\":\"21\",\"y1\":\"14\",\"y2\":\"3\"}],\"$undefined\"]}]]}]}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"rounded-lg border bg-card text-card-foreground shadow-sm md:col-span-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col space-y-1.5 p-6\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-2xl font-semibold leading-none tracking-tight flex items-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-smartphone h-5 w-5\",\"children\":[[\"$\",\"rect\",\"1yt0o3\",{\"width\":\"14\",\"height\":\"20\",\"x\":\"5\",\"y\":\"2\",\"rx\":\"2\",\"ry\":\"2\"}],[\"$\",\"path\",\"mhygvu\",{\"d\":\"M12 18h.01\"}],\"$undefined\"]}],\"Các dự án liên quan\"]}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground\",\"children\":\"Những ứng dụng khác mà chúng tôi đã phát triển cho sinh viên KMA\"}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 pt-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"grid gap-4 md:grid-cols-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-4 border rounded-lg\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-2\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-medium\",\"children\":\"KIT Schedule\"}],[\"$\",\"div\",null,{\"className\":\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground\",\"children\":\"Mobile App\"}]]}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground mb-3\",\"children\":\"Ứng dụng xem lịch học trên điện thoại dành cho sinh viên học viện KMA.\"}],[\"$\",\"a\",null,{\"href\":\"https://play.google.com/store/apps/details?id=kma.hatuan314.schedule\",\"target\":\"_blank\",\"rel\":\"noopener noreferrer\",\"className\":\"inline-flex items-center gap-1 text-sm text-primary hover:underline\",\"children\":[\"Xem trên Google Play\",[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link h-3 w-3\",\"children\":[[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],[\"$\",\"polyline\",\"mznyad\",{\"points\":\"15 3 21 3 21 9\"}],[\"$\",\"line\",\"18c3s4\",{\"x1\":\"10\",\"x2\":\"21\",\"y1\":\"14\",\"y2\":\"3\"}],\"$undefined\"]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"p-4 border rounded-lg\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-2\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-medium\",\"children\":\"KMA Tín chỉ\"}],[\"$\",\"div\",null,{\"className\":\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground\",\"children\":\"Web Tool\"}]]}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground mb-3\",\"children\":\"Tool hỗ trợ sinh viên sắp xếp lịch học hợp lí cho bản thân vào mỗi mùa đăng ký học.\"}],[\"$\",\"a\",null,{\"href\":\"https://github.com/ngosangns/tin-chi\",\"target\":\"_blank\",\"rel\":\"noopener noreferrer\",\"className\":\"inline-flex items-center gap-1 text-sm text-primary hover:underline\",\"children\":[\"Xem trên GitHub\",[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link h-3 w-3\",\"children\":[[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],[\"$\",\"polyline\",\"mznyad\",{\"points\":\"15 3 21 3 21 9\"}],[\"$\",\"line\",\"18c3s4\",{\"x1\":\"10\",\"x2\":\"21\",\"y1\":\"14\",\"y2\":\"3\"}],\"$undefined\"]}]]}]]}]]}]}]]}]]}]]}],null]]},[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"(main)\",\"children\",\"about\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"div\",null,{\"className\":\"flex flex-col min-h-screen\",\"children\":[[\"$\",\"$L7\",null,{}],[\"$\",\"main\",null,{\"className\":\"flex-1 container mx-auto px-4 py-6 max-w-7xl\",\"tabIndex\":-1,\"role\":\"main\",\"aria-label\":\"Main content\",\"children\":[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"(main)\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}],[\"$\",\"footer\",null,{\"className\":\"border-t bg-background\",\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"text-center text-sm text-muted-foreground\",\"children\":[[\"$\",\"p\",null,{\"children\":\"KMA Schedule v2022.12 - ngosangns\"}],[\"$\",\"p\",null,{\"className\":\"mt-1\",\"children\":\"Built with Next.js, TypeScript, and shadcn/ui\"}]]}]}]}]]}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"dark\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"$L8\",null,{\"children\":[\"$\",\"$L9\",null,{\"children\":[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$a\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$b\",\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":\"$c\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$d\",\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}]}]}],null]],\"initialHead\":[false,\"$Le\"],\"globalErrorComponent\":\"$f\"}]]\n"])</script><script>self.__next_f.push([1,"e:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"KMA Schedule\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"KMA Schedule - View your class schedule\"}]]\n4:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>