(()=>{var e={};e.id=287,e.ids=[287],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4689:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>h,pages:()=>d,routeModule:()=>x,tree:()=>o});var r=s(482),n=s(9108),a=s(2563),i=s.n(a),c=s(8300),l={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);s.d(t,l);let o=["",{children:["(main)",{children:["changelogs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,830)),"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/changelogs/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4173)),"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,2555)),"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/changelogs/page.tsx"],h="/(main)/changelogs/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/(main)/changelogs/page",pathname:"/changelogs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2798:(e,t,s)=>{Promise.resolve().then(s.bind(s,9705))},5303:()=>{},9705:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(2295),n=s(783),a=s.n(n),i=s(2254),c=s(2768),l=s(4513),o=s(8200),d=s(5094),h=s(5008),m=s(1453);let x=[{name:"Changelogs",href:"/changelogs"},{name:"About",href:"/about"}],p=[{name:"KIT Club",href:"https://www.facebook.com/kitclubKMA"},{name:"Issues",href:"https://github.com/ngosangns/kma-schedule-ngosangns/issues"}];function u(){let e=(0,i.usePathname)(),{sidebarOpen:t,toggleSidebar:s}=(0,h.useUI)();return r.jsx("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[r.jsx("div",{className:"flex items-center space-x-4",children:r.jsx(a(),{href:"/",className:"text-xl font-bold hover:text-primary transition-colors",children:"ACTVN SCHEDULE"})}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[x.map(t=>r.jsx(a(),{href:t.href,className:(0,m.cn)("text-sm font-medium transition-colors hover:text-primary",e===t.href?"text-primary":"text-muted-foreground"),children:t.name},t.name)),r.jsx("div",{className:"h-4 w-px bg-border"}),p.map(e=>(0,r.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-sm font-medium text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1",children:[e.name,r.jsx(c.Z,{className:"h-3 w-3"})]},e.name))]}),r.jsx(d.z,{variant:"ghost",size:"sm",className:"md:hidden",onClick:s,children:t?r.jsx(l.Z,{className:"h-5 w-5"}):r.jsx(o.Z,{className:"h-5 w-5"})})]}),t&&r.jsx("div",{className:"md:hidden border-t py-4",children:(0,r.jsxs)("nav",{className:"flex flex-col space-y-3",children:[x.map(t=>r.jsx(a(),{href:t.href,className:(0,m.cn)("text-sm font-medium transition-colors hover:text-primary px-2 py-1",e===t.href?"text-primary":"text-muted-foreground"),onClick:s,children:t.name},t.name)),r.jsx("div",{className:"h-px bg-border my-2"}),p.map(e=>(0,r.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-sm font-medium text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1 px-2 py-1",onClick:s,children:[e.name,r.jsx(c.Z,{className:"h-3 w-3"})]},e.name))]})})]})})}},830:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(5036),n=s(4551),a=s(1842),i=s(3805),c=s(9508);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,c.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),o=(0,c.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),d=(0,c.Z)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]]),h=(0,c.Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]),m=(0,c.Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]),x=(0,c.Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),p=[{version:"v2024.01",date:"2024-01-15",type:"major",title:"T\xe1i thiết kế ho\xe0n to\xe0n với shadcn/ui",changes:[{type:"feature",description:"Giao diện mới với shadcn/ui components"},{type:"feature",description:"Responsive design tối ưu cho mobile"},{type:"feature",description:"Dark mode mặc định"},{type:"feature",description:"Form validation với react-hook-form v\xe0 zod"},{type:"feature",description:"State management với React Context"},{type:"improvement",description:"Cải thiện performance v\xe0 loading states"},{type:"improvement",description:"Better error handling v\xe0 user feedback"}]},{version:"v2022.12",date:"2022-12-01",type:"minor",title:"Cải thiện UI/UX v\xe0 t\xednh năng",changes:[{type:"feature",description:"Th\xeam chế độ xem danh s\xe1ch"},{type:"feature",description:"Lọc theo buổi học (s\xe1ng, chiều, tối)"},{type:"improvement",description:"Cải thiện navigation giữa c\xe1c tuần"},{type:"fix",description:"Sửa lỗi hiển thị thời gian"}]},{version:"v2022.11",date:"2022-11-15",type:"minor",title:"T\xednh năng xuất lịch",changes:[{type:"feature",description:"Xuất thời kh\xf3a biểu sang Google Calendar"},{type:"improvement",description:"Cải thiện hiển thị th\xf4ng tin m\xf4n học"},{type:"fix",description:"Sửa lỗi đăng nhập với một số t\xe0i khoản"}]},{version:"v2022.10",date:"2022-10-01",type:"major",title:"Phi\xean bản đầu ti\xean",changes:[{type:"feature",description:"Đăng nhập với t\xe0i khoản ACTVN"},{type:"feature",description:"Xem thời kh\xf3a biểu theo tuần"},{type:"feature",description:"Chuyển đổi giữa c\xe1c học kỳ"},{type:"feature",description:"Giao diện web responsive"}]}],u=e=>{switch(e){case"feature":return r.jsx(l,{className:"h-4 w-4 text-green-500"});case"improvement":return r.jsx(o,{className:"h-4 w-4 text-blue-500"});case"fix":return r.jsx(d,{className:"h-4 w-4 text-red-500"});case"security":return r.jsx(h,{className:"h-4 w-4 text-purple-500"});case"design":return r.jsx(m,{className:"h-4 w-4 text-pink-500"});default:return r.jsx(x,{className:"h-4 w-4 text-gray-500"})}},g=e=>{switch(e){case"major":return r.jsx(a.C,{variant:"default",children:"Major"});case"minor":return r.jsx(a.C,{variant:"secondary",children:"Minor"});case"patch":return r.jsx(a.C,{variant:"outline",children:"Patch"});default:return r.jsx(a.C,{variant:"outline",children:"Release"})}};function f(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Lịch sử cập nhật"}),r.jsx("p",{className:"text-muted-foreground",children:"Theo d\xf5i c\xe1c t\xednh năng mới v\xe0 cải tiến của ACTVN Schedule"})]}),r.jsx("div",{className:"space-y-6",children:p.map((e,t)=>(0,r.jsxs)(n.Zb,{children:[r.jsx(n.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(n.ll,{className:"text-xl",children:e.version}),g(e.type)]}),r.jsx(n.SZ,{children:e.title})]}),r.jsx("div",{className:"text-sm text-muted-foreground",children:new Date(e.date).toLocaleDateString("vi-VN")})]})}),r.jsx(n.aY,{children:r.jsx("div",{className:"space-y-3",children:e.changes.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[u(e.type),r.jsx("span",{className:"text-sm",children:e.description})]},t))})}),t<p.length-1&&r.jsx(i.Z,{className:"mt-6"})]},e.version))}),(0,r.jsxs)(n.Zb,{children:[r.jsx(n.Ol,{children:r.jsx(n.ll,{className:"text-lg",children:"Ch\xfa th\xedch"})}),r.jsx(n.aY,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(l,{className:"h-4 w-4 text-green-500"}),r.jsx("span",{children:"T\xednh năng mới"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(o,{className:"h-4 w-4 text-blue-500"}),r.jsx("span",{children:"Cải tiến"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(d,{className:"h-4 w-4 text-red-500"}),r.jsx("span",{children:"Sửa lỗi"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(h,{className:"h-4 w-4 text-purple-500"}),r.jsx("span",{children:"Bảo mật"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(m,{className:"h-4 w-4 text-pink-500"}),r.jsx("span",{children:"Thiết kế"})]})]})})]})]})}},4173:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(5036);let n=(0,s(6843).createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Header.tsx`),{__esModule:a,$$typeof:i}=n,c=n.default;function l(){return r.jsx("footer",{className:"border-t bg-background",children:r.jsx("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[r.jsx("p",{children:"KMA Schedule v2022.12 - ngosangns"}),r.jsx("p",{className:"mt-1",children:"Built with Next.js, TypeScript, and shadcn/ui"})]})})})}function o({children:e}){return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen",children:[r.jsx(c,{}),r.jsx("main",{className:"flex-1 container mx-auto px-4 py-6 max-w-7xl",tabIndex:-1,role:"main","aria-label":"Main content",children:e}),r.jsx(l,{})]})}},1842:(e,t,s)=>{"use strict";s.d(t,{C:()=>c});var r=s(5036);s(2);var n=s(4467),a=s(1171);let i=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,...s}){return r.jsx("div",{className:(0,a.cn)(i({variant:t}),e),...s})}},4551:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>c,SZ:()=>o,Zb:()=>i,aY:()=>d,ll:()=>l});var r=s(5036),n=s(2),a=s(1171);let i=n.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let c=n.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));c.displayName="CardHeader";let l=n.forwardRef(({className:e,...t},s)=>r.jsx("h3",{ref:s,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let o=n.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));o.displayName="CardDescription";let d=n.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,a.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",n.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},3805:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(5036),n=s(2),a=s(5367),i=s(1171);let c=n.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...n},c)=>r.jsx(a.f,{ref:c,decorative:s,orientation:t,className:(0,i.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...n}));c.displayName=a.f.displayName},1171:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a});var r=s(990),n=s(1774);function a(...e){return(0,n.m6)((0,r.W)(e))}s(5603)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[21,795,498,584],()=>s(4689));module.exports=r})();