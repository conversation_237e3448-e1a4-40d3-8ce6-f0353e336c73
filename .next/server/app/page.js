(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},201:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>g,tree:()=>l});var n=s(482),r=s(9108),a=s(2563),i=s.n(a),o=s(8300),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);s.d(t,u);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1136)),"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,2555)),"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Github/kma-schedule-ngosangns/src/app/page.tsx"],d="/page",p={require:s,loadChunk:()=>Promise.resolve()},g=new n.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2083:(e,t,s)=>{Promise.resolve().then(s.bind(s,1532))},1532:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var n=s(2295),r=s(3729),a=s(2254),i=s(5008),o=s(2053);function u(){let e=(0,a.useRouter)(),{isAuthenticated:t,isLoading:s}=(0,i.useAuth)();return(0,r.useEffect)(()=>{s||(t?e.push("/calendar"):e.push("/login"))},[t,s,e]),n.jsx(o.w,{text:"Đang kiểm tra đăng nhập..."})}},2053:(e,t,s)=>{"use strict";s.d(t,{T:()=>o,w:()=>u});var n=s(2295);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var a=s(1453);let i={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"};function o({size:e="md",className:t,text:s}){return(0,n.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[n.jsx(r,{className:(0,a.cn)("animate-spin",i[e],t)}),s&&n.jsx("span",{className:"text-sm text-muted-foreground",children:s})]})}function u({text:e="Đang tải..."}){return n.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:n.jsx(o,{size:"lg",text:e})})}},1136:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>r,default:()=>i});let n=(0,s(6843).createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/app/page.tsx`),{__esModule:r,$$typeof:a}=n,i=n.default}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[21,584],()=>s(201));module.exports=n})();