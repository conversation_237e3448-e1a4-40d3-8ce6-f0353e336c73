(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{8156:function(e,s,t){Promise.resolve().then(t.bind(t,464))},464:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return I}});var a=t(7437),l=t(2265),r=t(4033),n=t(5671),i=t(575),c=t(6061),d=t(2169);let o=(0,c.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function m(e){let{className:s,variant:t,...l}=e;return(0,a.jsx)("div",{className:(0,d.cn)(o({variant:t}),s),...l})}var h=t(5646),x=t(3523),u=t(9224),f=t(2442);let j=h.fC;h.ZA;let p=h.B4,g=l.forwardRef((e,s)=>{let{className:t,children:l,...r}=e;return(0,a.jsxs)(h.xz,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...r,children:[l,(0,a.jsx)(h.JO,{asChild:!0,children:(0,a.jsx)(x.Z,{className:"h-4 w-4 opacity-50"})})]})});g.displayName=h.xz.displayName;let N=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(h.u_,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,a.jsx)(u.Z,{className:"h-4 w-4"})})});N.displayName=h.u_.displayName;let b=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(h.$G,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,a.jsx)(x.Z,{className:"h-4 w-4"})})});b.displayName=h.$G.displayName;let w=l.forwardRef((e,s)=>{let{className:t,children:l,position:r="popper",...n}=e;return(0,a.jsx)(h.h_,{children:(0,a.jsxs)(h.VY,{ref:s,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...n,children:[(0,a.jsx)(N,{}),(0,a.jsx)(h.l_,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,a.jsx)(b,{})]})})});w.displayName=h.VY.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(h.__,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...l})}).displayName=h.__.displayName;let v=l.forwardRef((e,s)=>{let{className:t,children:l,...r}=e;return(0,a.jsxs)(h.ck,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(h.wU,{children:(0,a.jsx)(f.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(h.eT,{children:l})]})});v.displayName=h.ck.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(h.Z0,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...l})}).displayName=h.Z0.displayName;let y=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:s,className:(0,d.cn)("w-full caption-bottom text-sm",t),...l})})});y.displayName="Table";let k=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("thead",{ref:s,className:(0,d.cn)("[&_tr]:border-b",t),...l})});k.displayName="TableHeader";let Z=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("tbody",{ref:s,className:(0,d.cn)("[&_tr:last-child]:border-0",t),...l})});Z.displayName="TableBody",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("tfoot",{ref:s,className:(0,d.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...l})}).displayName="TableFooter";let _=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("tr",{ref:s,className:(0,d.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...l})});_.displayName="TableRow";let z=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("th",{ref:s,className:(0,d.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...l})});z.displayName="TableHead";let C=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("td",{ref:s,className:(0,d.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...l})});C.displayName="TableCell",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("caption",{ref:s,className:(0,d.cn)("mt-4 text-sm text-muted-foreground",t),...l})}).displayName="TableCaption";var T=t(5818);function R(e){let{icon:s,title:t,description:l,action:r,className:c}=e;return(0,a.jsx)("div",{className:"flex items-center justify-center p-8 ".concat(c),children:(0,a.jsxs)(n.Zb,{className:"w-full max-w-md text-center",children:[(0,a.jsxs)(n.Ol,{children:[s&&(0,a.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted",children:(0,a.jsx)(s,{className:"h-6 w-6 text-muted-foreground"})}),(0,a.jsx)(n.ll,{className:"text-lg",children:t}),l&&(0,a.jsx)(n.SZ,{className:"text-sm",children:l})]}),r&&(0,a.jsx)(n.aY,{children:(0,a.jsx)(i.z,{onClick:r.onClick,className:"w-full",children:r.label})})]})})}var S=t(5817),D=t(5883),Y=t(1813),F=t(4689),H=t(1291),V=t(7158),B=t(6142),E=t(8203),M=t(9865),O=t(5300),G=t(5306),J=t(8104),K=t(4946),A=t(5474);function I(){var e;let s=(0,r.useRouter)(),{user:t,logout:c}=(0,O.useAuth)(),{calendar:o,student:h,setCalendar:x,setStudent:u}=(0,O.useCalendar)(),{showSuccess:f,showError:N}=(0,G.z)(),[b,I]=(0,l.useState)(!1),[P,U]=(0,l.useState)(0),[$,q]=(0,l.useState)([]),[X,L]=(0,l.useState)("calendar"),[Q,W]=(0,l.useState)("all"),ee=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4,t=[],a=new Date;for(let l=0;l<s;l++){let s=new Date(a);s.setDate(a.getDate()-a.getDay()+1+(e+l)*7);let r=[];for(let e=0;e<7;e++){let t=new Date(s);t.setDate(s.getDate()+e),r.push({time:t.getTime(),shift:[]})}t.push(r)}return t},[es,et]=(0,l.useState)({calendar:null,student:null,semesters:null,mainForm:null,signInToken:null});(0,l.useEffect)(()=>{let e=(0,J.mu)();if(e&&e.calendar&&e.calendar.weeks&&e.calendar.weeks.length>0)et(e),ea(0,e.calendar);else if(e&&e.calendar){let s=ee(-1,4),t={...e.calendar,weeks:s};et({...e,calendar:t}),q(s[1]),U(1)}else{let e=ee(-1,4);et({calendar:{data_subject:[],weeks:e},student:null,semesters:null,mainForm:null,signInToken:null}),q(e[1]),U(1)}},[]);let ea=(e,s)=>{let t=s||es.calendar;if(!t||!t.weeks||0===t.weeks.length)return;let a=Math.max(0,Math.min(e,t.weeks.length-1));q(t.weeks[a]),U(a)},el=async e=>{if(!es.semesters||!es.mainForm||!es.signInToken)return;let{semesters:s,mainForm:t,signInToken:a}=es,l=s.currentSemester;if(e!==l){I(!0);try{let l={...t,drpSemester:e},r={...s,currentSemester:e};et(e=>({...e,semesters:r}));let n=await (0,K.hz)(l,a),i=(0,K.Pn)(n),c=await (0,K._b)(i),d=(0,K.cD)(i),o=(0,K.ew)(i),m=(0,K.VZ)(i),h={mainForm:o,semesters:m,calendar:c,student:d};et(e=>({...e,...h})),x(c),u(d),(0,J.OH)(h),ea(0,c),f("Đ\xe3 cập nhật học kỳ th\xe0nh c\xf4ng!")}catch(t){console.error("Semester change error:",t),N("C\xf3 lỗi xảy ra khi lấy dữ liệu!");let e={...s,currentSemester:l};et(s=>({...s,semesters:e}))}finally{I(!1)}}},er=()=>{if(!$||!$.length)return[];let e=[];return $.forEach(s=>{s.shift&&s.shift.length>0&&(e=[...e,...s.shift])}),"all"!==Q&&(e=e.filter(e=>(0,d.kJ)(e.shift)===Q)),e.sort((e,s)=>e.day!==s.day?e.day-s.day:e.shift-s.shift)};if(!es.calendar)return(0,a.jsx)(T.w,{text:"Đang tải thời kh\xf3a biểu..."});let en=es.calendar.data_subject&&es.calendar.data_subject.length>0,ei=es.calendar.weeks&&es.calendar.weeks.length>0;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Thời kh\xf3a biểu"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:h||(null==t?void 0:t.name)||"Sinh vi\xean"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(i.z,{onClick:()=>{h&&o&&((0,K.qs)(h,o),f("Đ\xe3 xuất lịch th\xe0nh c\xf4ng!"))},variant:"outline",size:"sm",disabled:!h||!o||!(null===(e=o.data_subject)||void 0===e?void 0:e.length),children:[(0,a.jsx)(S.Z,{className:"w-4 h-4 mr-2"}),"Xuất Google Calendar"]}),(0,a.jsxs)(i.z,{onClick:()=>{(0,A.k)(),c(),s.push("/login")},variant:"outline",size:"sm",children:[(0,a.jsx)(D.Z,{className:"w-4 h-4 mr-2"}),"Đăng xuất"]})]})]}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Học kỳ:"}),es.semesters&&es.semesters.semesters&&(0,a.jsxs)(j,{value:es.semesters.currentSemester,onValueChange:el,disabled:b,children:[(0,a.jsx)(g,{className:"w-[200px]",children:(0,a.jsx)(p,{})}),(0,a.jsx)(w,{children:es.semesters.semesters.map(e=>(0,a.jsxs)(v,{value:e.value,children:[e.th,"_",e.from,"_",e.to]},e.value))})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 border rounded-md",children:[(0,a.jsx)(i.z,{variant:"calendar"===X?"default":"ghost",size:"sm",onClick:()=>L("calendar"),children:(0,a.jsx)(Y.Z,{className:"w-4 h-4"})}),(0,a.jsx)(i.z,{variant:"list"===X?"default":"ghost",size:"sm",onClick:()=>L("list"),children:(0,a.jsx)(F.Z,{className:"w-4 h-4"})})]}),(0,a.jsxs)(j,{value:Q,onValueChange:W,children:[(0,a.jsx)(g,{className:"w-[140px]",children:(0,a.jsx)(p,{})}),(0,a.jsxs)(w,{children:[(0,a.jsx)(v,{value:"all",children:"Tất cả"}),(0,a.jsx)(v,{value:"morning",children:"Buổi s\xe1ng"}),(0,a.jsx)(v,{value:"afternoon",children:"Buổi chiều"}),(0,a.jsx)(v,{value:"evening",children:"Buổi tối"})]})]})]})]})})}),b&&(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-8",children:(0,a.jsx)(T.T,{size:"lg",text:"Đang tải dữ liệu..."})})}),(ei||$&&$.length>0)&&(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>ea(P-1),disabled:!es.calendar.weeks||0===P,children:[(0,a.jsx)(H.Z,{className:"w-4 h-4 mr-2"}),"Tuần trước"]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"font-medium",children:es.calendar.weeks&&es.calendar.weeks.length>0?(0,a.jsxs)(a.Fragment,{children:["Tuần ",P+1," / ",es.calendar.weeks.length]}):(0,a.jsx)(a.Fragment,{children:"Tuần hiện tại"})}),$&&$.length>0&&(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[(0,d.p6)($[0].time)," -"," ",(0,d.p6)($[$.length-1].time)]})]}),(0,a.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>ea(P+1),disabled:!es.calendar.weeks||P===es.calendar.weeks.length-1,children:["Tuần sau",(0,a.jsx)(V.Z,{className:"w-4 h-4 ml-2"})]})]})})}),"calendar"===X&&($&&$.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-4",children:$.map((e,s)=>(0,a.jsx)(n.Zb,{className:"min-h-[200px]",children:(0,a.jsxs)(n.aY,{className:"p-3",children:[(0,a.jsxs)("div",{className:"text-center mb-3",children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:(0,d.UZ)(new Date(e.time).getDay())}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,d.p6)(e.time,"DD/MM")})]}),(0,a.jsx)("div",{className:"space-y-2",children:e.shift&&e.shift.length>0?e.shift.filter(e=>"all"===Q||(0,d.kJ)(e.shift)===Q).map((e,s)=>{let t=(0,d.N8)(e.shift),l=(0,d.kJ)(e.shift);return(0,a.jsxs)("div",{className:"p-2 rounded-md border bg-card text-card-foreground text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 mb-1",children:[(0,a.jsxs)(m,{variant:"morning"===l?"default":"afternoon"===l?"secondary":"outline",className:"text-xs px-1 py-0",children:["Ca ",e.shift]}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:t.start})]}),(0,a.jsx)("p",{className:"font-medium text-xs mb-1 line-clamp-2",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,a.jsx)(B.Z,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"truncate",children:e.room})]})]},s)}):(0,a.jsx)("p",{className:"text-xs text-muted-foreground text-center py-4",children:"Kh\xf4ng c\xf3 lịch học"})})]})},s))}):(0,a.jsx)(R,{icon:E.Z,title:"Kh\xf4ng c\xf3 dữ liệu lịch học",description:"Học kỳ n\xe0y chưa c\xf3 lịch học hoặc chưa được cập nhật."})),"list"===X&&(0,a.jsx)(n.Zb,{children:(0,a.jsxs)(n.aY,{className:"p-0",children:[(0,a.jsxs)(y,{children:[(0,a.jsx)(k,{children:(0,a.jsxs)(_,{children:[(0,a.jsx)(z,{children:"Thời gian"}),(0,a.jsx)(z,{children:"M\xf4n học"}),(0,a.jsx)(z,{children:"Ph\xf2ng"}),(0,a.jsx)(z,{children:"Giảng vi\xean"})]})}),(0,a.jsx)(Z,{children:er().map((e,s)=>{let t=(0,d.N8)(e.shift);return(0,a.jsxs)(_,{children:[(0,a.jsx)(C,{children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("span",{className:"font-medium",children:[(0,d.UZ)(e.day)," - Ca ",e.shift]}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[t.start," - ",t.end]})]})}),(0,a.jsx)(C,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]})}),(0,a.jsx)(C,{children:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(B.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.room})]})}),(0,a.jsx)(C,{children:e.instructor||"N/A"})]},s)})})]}),0===er().length&&(0,a.jsx)("div",{className:"p-8 text-center",children:(0,a.jsx)(R,{icon:E.Z,title:"Kh\xf4ng c\xf3 lịch học",description:en?"Kh\xf4ng c\xf3 lịch học n\xe0o trong tuần n\xe0y với bộ lọc đ\xe3 chọn.":"Học kỳ n\xe0y chưa c\xf3 lịch học hoặc chưa được cập nhật."})})]})})]})}}},function(e){e.O(0,[990,666,213,640,710,972,971,938,744],function(){return e(e.s=8156)}),_N_E=e.O()}]);