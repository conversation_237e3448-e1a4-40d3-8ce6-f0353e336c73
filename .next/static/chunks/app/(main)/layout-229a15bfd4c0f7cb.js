(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[95],{9443:function(e,t,n){Promise.resolve().then(n.bind(n,2767))},2767:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return g}});var r=n(7437),a=n(1396),s=n.n(a),o=n(4033),i=n(3711),u=n(2549),l=n(8004),d=n(575),c=n(5300),m=n(2169);let f=[{name:"Changelogs",href:"/changelogs"},{name:"About",href:"/about"}],h=[{name:"KIT Club",href:"https://www.facebook.com/kitclubKMA"},{name:"Issues",href:"https://github.com/ngosangns/kma-schedule-ngosangns/issues"}];function g(){let e=(0,o.usePathname)(),{sidebarOpen:t,toggleSidebar:n}=(0,c.useUI)();return(0,r.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)(s(),{href:"/",className:"text-xl font-bold hover:text-primary transition-colors",children:"ACTVN SCHEDULE"})}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[f.map(t=>(0,r.jsx)(s(),{href:t.href,className:(0,m.cn)("text-sm font-medium transition-colors hover:text-primary",e===t.href?"text-primary":"text-muted-foreground"),children:t.name},t.name)),(0,r.jsx)("div",{className:"h-4 w-px bg-border"}),h.map(e=>(0,r.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-sm font-medium text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1",children:[e.name,(0,r.jsx)(i.Z,{className:"h-3 w-3"})]},e.name))]}),(0,r.jsx)(d.z,{variant:"ghost",size:"sm",className:"md:hidden",onClick:n,children:t?(0,r.jsx)(u.Z,{className:"h-5 w-5"}):(0,r.jsx)(l.Z,{className:"h-5 w-5"})})]}),t&&(0,r.jsx)("div",{className:"md:hidden border-t py-4",children:(0,r.jsxs)("nav",{className:"flex flex-col space-y-3",children:[f.map(t=>(0,r.jsx)(s(),{href:t.href,className:(0,m.cn)("text-sm font-medium transition-colors hover:text-primary px-2 py-1",e===t.href?"text-primary":"text-muted-foreground"),onClick:n,children:t.name},t.name)),(0,r.jsx)("div",{className:"h-px bg-border my-2"}),h.map(e=>(0,r.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-sm font-medium text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1 px-2 py-1",onClick:n,children:[e.name,(0,r.jsx)(i.Z,{className:"h-3 w-3"})]},e.name))]})})]})})}},575:function(e,t,n){"use strict";n.d(t,{z:function(){return l}});var r=n(7437),a=n(2265),s=n(7256),o=n(6061),i=n(2169);let u=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:n,variant:a,size:o,asChild:l=!1,...d}=e,c=l?s.g7:"button";return(0,r.jsx)(c,{className:(0,i.cn)(u({variant:a,size:o,className:n})),ref:t,...d})});l.displayName="Button"},5300:function(e,t,n){"use strict";n.r(t),n.d(t,{AppProvider:function(){return l},useApp:function(){return d},useAuth:function(){return c},useCalendar:function(){return m},useUI:function(){return f}});var r=n(7437),a=n(2265),s=n(8104);let o={auth:{user:null,isAuthenticated:!1,isLoading:!1,error:null},calendar:null,ui:{theme:"dark",sidebarOpen:!1,currentView:"calendar"},student:null};function i(e,t){switch(t.type){case"AUTH_START":return{...e,auth:{...e.auth,isLoading:!0,error:null}};case"AUTH_SUCCESS":return{...e,auth:{user:t.payload.user,isAuthenticated:!0,isLoading:!1,error:null}};case"AUTH_ERROR":return{...e,auth:{user:null,isAuthenticated:!1,isLoading:!1,error:t.payload}};case"AUTH_LOGOUT":return{...e,auth:{user:null,isAuthenticated:!1,isLoading:!1,error:null},calendar:null,student:null};case"SET_CALENDAR":return{...e,calendar:t.payload};case"SET_STUDENT":return{...e,student:t.payload};case"SET_THEME":return{...e,ui:{...e.ui,theme:t.payload}};case"TOGGLE_SIDEBAR":return{...e,ui:{...e.ui,sidebarOpen:!e.ui.sidebarOpen}};case"SET_VIEW":return{...e,ui:{...e.ui,currentView:t.payload}};case"LOAD_FROM_STORAGE":let{signInToken:n,calendar:r,student:a,user:s}=t.payload;return{...e,auth:{user:s||null,isAuthenticated:!!(n||r),isLoading:!1,error:null},calendar:r||null,student:a||null};default:return e}}let u=(0,a.createContext)(null);function l(e){let{children:t}=e,[n,l]=(0,a.useReducer)(i,o);return(0,a.useEffect)(()=>{let e=(0,s.mu)();e&&l({type:"LOAD_FROM_STORAGE",payload:e})},[]),(0,a.useEffect)(()=>{if(n.auth.isAuthenticated&&n.calendar){let e={calendar:n.calendar,student:n.student||void 0,user:n.auth.user||void 0};(0,s.OH)(e)}},[n.auth.isAuthenticated,n.calendar,n.student,n.auth.user]),(0,r.jsx)(u.Provider,{value:{state:n,dispatch:l},children:t})}function d(){let e=(0,a.useContext)(u);if(!e)throw Error("useApp must be used within an AppProvider");return e}function c(){let{state:e,dispatch:t}=d();return{...e.auth,login:(e,n)=>t({type:"AUTH_SUCCESS",payload:{user:e,signInToken:n}}),logout:()=>t({type:"AUTH_LOGOUT"}),setLoading:()=>t({type:"AUTH_START"}),setError:e=>t({type:"AUTH_ERROR",payload:e})}}function m(){let{state:e,dispatch:t}=d();return{calendar:e.calendar,student:e.student,setCalendar:e=>t({type:"SET_CALENDAR",payload:e}),setStudent:e=>t({type:"SET_STUDENT",payload:e})}}function f(){let{state:e,dispatch:t}=d();return{...e.ui,setTheme:e=>t({type:"SET_THEME",payload:e}),toggleSidebar:()=>t({type:"TOGGLE_SIDEBAR"}),setView:e=>t({type:"SET_VIEW",payload:e})}}},8104:function(e,t,n){"use strict";function r(e){e.semesters&&window.localStorage.setItem("semesters",JSON.stringify(e.semesters)),e.signInToken&&e.signInToken.length&&window.localStorage.setItem("signInToken",e.signInToken),e.mainForm&&window.localStorage.setItem("mainForm",JSON.stringify(e.mainForm)),e.calendar&&window.localStorage.setItem("calendar",JSON.stringify(e.calendar)),e.student&&window.localStorage.setItem("student",e.student)}function a(){let e=window.localStorage.getItem("calendar"),t=window.localStorage.getItem("student"),n=window.localStorage.getItem("semesters"),r=window.localStorage.getItem("mainForm"),a=window.localStorage.getItem("signInToken");return{calendar:e?JSON.parse(e):null,student:t||null,semesters:n?JSON.parse(n):null,mainForm:r?JSON.parse(r):null,signInToken:a||null}}function s(){window.localStorage.removeItem("calendar"),window.localStorage.removeItem("student"),window.localStorage.removeItem("semesters"),window.localStorage.removeItem("mainForm"),window.localStorage.removeItem("signInToken")}n.d(t,{Nk:function(){return s},OH:function(){return r},mu:function(){return a}})},2169:function(e,t,n){"use strict";n.d(t,{N8:function(){return d},UZ:function(){return m},cn:function(){return i},e$:function(){return l},kJ:function(){return c},p6:function(){return u}});var r=n(7042),a=n(4769),s=n(2067),o=n.n(s);function i(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.m6)((0,r.W)(t))}function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"DD/MM/YYYY";return o()(e).format(t)}function l(e){return e instanceof Error?e.message:"string"==typeof e?e:"Đ\xe3 xảy ra lỗi kh\xf4ng x\xe1c định"}function d(e){return({1:{start:"07:00",end:"07:50"},2:{start:"08:00",end:"08:50"},3:{start:"09:00",end:"09:50"},4:{start:"10:00",end:"10:50"},5:{start:"11:00",end:"11:50"},6:{start:"12:00",end:"12:50"},7:{start:"13:00",end:"13:50"},8:{start:"14:00",end:"14:50"},9:{start:"15:00",end:"15:50"},10:{start:"16:00",end:"16:50"},11:{start:"17:00",end:"17:50"},12:{start:"18:00",end:"18:50"},13:{start:"19:00",end:"19:50"},14:{start:"20:00",end:"20:50"},15:{start:"21:00",end:"21:50"}})[e]||{start:"00:00",end:"00:00"}}function c(e){return e>=1&&e<=6?"morning":e>=7&&e<=12?"afternoon":"evening"}function m(e){return["Chủ nhật","Thứ hai","Thứ ba","Thứ tư","Thứ năm","Thứ s\xe1u","Thứ bảy"][e]||"Kh\xf4ng x\xe1c định"}}},function(e){e.O(0,[990,666,485,971,938,744],function(){return e(e.s=9443)}),_N_E=e.O()}]);