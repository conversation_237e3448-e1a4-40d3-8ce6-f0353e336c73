(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{6573:function(e,r,t){Promise.resolve().then(t.t.bind(t,3385,23)),Promise.resolve().then(t.bind(t,5185)),Promise.resolve().then(t.bind(t,5300))},5185:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return _}});var s=t(7437),a=t(2265),o=t(4033),n=t(5300),i=t(6862),l=t(1299),d=t(6061),c=t(2549),u=t(2169);let f=l.zt,m=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(l.l_,{ref:r,className:(0,u.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),...a})});m.displayName=l.l_.displayName;let p=(0,d.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),x=a.forwardRef((e,r)=>{let{className:t,variant:a,...o}=e;return(0,s.jsx)(l.fC,{ref:r,className:(0,u.cn)(p({variant:a}),t),...o})});x.displayName=l.fC.displayName,a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(l.aU,{ref:r,className:(0,u.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",t),...a})}).displayName=l.aU.displayName;let h=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(l.x8,{ref:r,className:(0,u.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),"toast-close":"",...a,children:(0,s.jsx)(c.Z,{className:"h-4 w-4"})})});h.displayName=l.x8.displayName;let g=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(l.Dx,{ref:r,className:(0,u.cn)("text-sm font-semibold",t),...a})});g.displayName=l.Dx.displayName;let v=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(l.dk,{ref:r,className:(0,u.cn)("text-sm opacity-90",t),...a})});function j(){let{toasts:e}=(0,i.pm)();return(0,s.jsxs)(f,{children:[e.map(function(e){let{id:r,title:t,description:a,action:o,...n}=e;return(0,s.jsxs)(x,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&(0,s.jsx)(g,{children:t}),a&&(0,s.jsx)(v,{children:a})]}),o,(0,s.jsx)(h,{})]},r)}),(0,s.jsx)(m,{})]})}v.displayName=l.dk.displayName;var b=t(2894),N=t(4280),w=t(575),y=t(5671);class k extends a.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("Error caught by boundary:",e,r)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return(0,s.jsx)(e,{error:this.state.error,resetError:this.resetError})}return(0,s.jsx)(E,{error:this.state.error,resetError:this.resetError})}return this.props.children}constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}}function E(e){let{error:r,resetError:t}=e;return(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px] p-4",children:(0,s.jsxs)(y.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(y.Ol,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10",children:(0,s.jsx)(b.Z,{className:"h-6 w-6 text-destructive"})}),(0,s.jsx)(y.ll,{children:"C\xf3 lỗi xảy ra"}),(0,s.jsx)(y.SZ,{children:"Ứng dụng đ\xe3 gặp phải một lỗi kh\xf4ng mong muốn."})]}),(0,s.jsxs)(y.aY,{className:"space-y-4",children:[(0,s.jsxs)("details",{className:"text-sm",children:[(0,s.jsx)("summary",{className:"cursor-pointer text-muted-foreground hover:text-foreground",children:"Chi tiết lỗi"}),(0,s.jsx)("pre",{className:"mt-2 whitespace-pre-wrap break-words text-xs bg-muted p-2 rounded",children:r.message})]}),(0,s.jsxs)(w.z,{onClick:t,className:"w-full",children:[(0,s.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"Thử lại"]})]})]})})}function C(){return(0,s.jsx)(w.z,{onClick:()=>{let e=document.querySelector("main");e&&(e.focus(),e.scrollIntoView())},className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50",variant:"outline",children:"Skip to main content"})}function _(e){let{children:r}=e,{isAuthenticated:t,isLoading:i}=(0,n.useAuth)(),l=(0,o.useRouter)(),d=(0,o.usePathname)();return((0,a.useEffect)(()=>{if(!i){let e="/login"===d,r="/"===d||"/about"===d||"/changelogs"===d;t||e||r?t&&e&&l.push("/calendar"):l.push("/login")}},[t,i,d,l]),i)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):(0,s.jsxs)(k,{children:[(0,s.jsx)(C,{}),(0,s.jsxs)("div",{className:(0,u.cn)("min-h-screen bg-background text-foreground","flex flex-col"),children:[r,(0,s.jsx)(j,{})]})]})}},3385:function(){}},function(e){e.O(0,[990,666,213,515,710,971,938,744],function(){return e(e.s=6573)}),_N_E=e.O()}]);