(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[156],{1814:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/LoginPage",function(){return r(1695)}])},1695:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return F}});var n=r(5893),s=r(7294),a=r(9332),l=r(512),i=r(8388);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,l.W)(t))}r(381);let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:o("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});d.displayName="Card";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:o("flex flex-col space-y-1.5 p-6",r),...s})});c.displayName="CardHeader";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h3",{ref:t,className:o("text-2xl font-semibold leading-none tracking-tight",r),...s})});u.displayName="CardTitle",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("p",{ref:t,className:o("text-sm text-muted-foreground",r),...s})}).displayName="CardDescription";let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:o("p-6 pt-0",r),...s})});m.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:o("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter";var h=r(1465),f=r(2003);let g=(0,f.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),p=s.forwardRef((e,t)=>{let{className:r,variant:s,size:a,asChild:l=!1,...i}=e,d=l?h.g7:"button";return(0,n.jsx)(d,{className:o(g({variant:s,size:a,className:r})),ref:t,...i})});p.displayName="Button";let x=s.forwardRef((e,t)=>{let{className:r,type:s,...a}=e;return(0,n.jsx)("input",{type:s,className:o("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...a})});x.displayName="Input";var v=r(3440);let b=(0,f.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),y=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(v.f,{ref:t,className:o(b(),r),...s})});y.displayName=v.f.displayName;let w=(0,f.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),j=s.forwardRef((e,t)=>{let{className:r,variant:s,...a}=e;return(0,n.jsx)("div",{ref:t,role:"alert",className:o(w({variant:s}),r),...a})});j.displayName="Alert",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h5",{ref:t,className:o("mb-1 font-medium leading-none tracking-tight",r),...s})}).displayName="AlertTitle";let N=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:o("text-sm [&_p]:leading-relaxed",r),...s})});N.displayName="AlertDescription";let T=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("textarea",{className:o("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...s})});T.displayName="Textarea";var _=r(3217),k=r(7653),S=r(8814);function I(e){e.semesters&&window.localStorage.setItem("semesters",JSON.stringify(e.semesters)),e.signInToken&&e.signInToken.length&&window.localStorage.setItem("signInToken",e.signInToken),e.mainForm&&window.localStorage.setItem("mainForm",JSON.stringify(e.mainForm)),e.calendar&&window.localStorage.setItem("calendar",JSON.stringify(e.calendar)),e.student&&window.localStorage.setItem("student",e.student)}async function E(e){let t=await fetch("https://actvn-schedule.cors-ngosangns.workers.dev/subject",{method:"GET",headers:{"x-cors-headers":JSON.stringify({Cookie:e})}});return await t.text()}function C(e,t){let r=e.match(RegExp('id="'+t+'" value="(.+?)"',"g"));return!!(r&&r.length&&(r=(r=r[0]).match(/value="(.+?)"/))&&r.length)&&(r=r[1])}function O(e){return e.replace(/src="(.+?)"/g,"")}function R(e){let t=e.match(/<span id="lblStudent">(.+?)<\/span/g);return t&&t.length&&(t=t[0].match(/<span id="lblStudent">(.+?)<\/span/))&&t.length>1?t[1]:"KIT Club"}async function D(e){if(!e)throw Error("empty data");return await new Promise((t,r)=>{let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];let s="self.onmessage = "+e.toString();for(let e of r)s+="\n"+e.toString();let a=new Blob([s],{type:"text/javascript"}),l=URL.createObjectURL(a);return new Worker(l)}(e=>self.postMessage(H(e.data)),H);n.onmessage=e=>t(e.data?e.data:!1===e.data?{data_subject:[]}:null),n.onerror=e=>r(e),n.postMessage(function(e){if(!e||!e.length)return!1;let t=(e=(e=e.replace(/ {2,}/gm," ")).replace(/<!--.*?-->|\t|(?:\r?\n[ \t]*)+/gm,"")).match(/<table.+?gridRegistered.+?<\/table>/g);if(t&&t.length&&(e=t[0]),"undefined"==typeof document)throw Error("DOM operations not available on server side");let r=document.createElement("div");r.id="cleanTKB",r.style.display="none",r.innerHTML=e,document.body.appendChild(r);let n=Array.prototype.map.call(r.querySelectorAll("#gridRegistered tr"),e=>Array.prototype.map.call(e.querySelectorAll("td"),e=>{var t;return null===(t=e.innerHTML)||!1===t?"":(t=t.toString()).replace(/<[^>]*>/g,"")}));return document.body.removeChild(r),!!n&&n}(e))}).catch(e=>{throw e})}function A(e){if("undefined"==typeof DOMParser)throw Error("DOMParser not available on server side");let t=new DOMParser().parseFromString(e,"text/html").getElementById("Form1");if(!t)return{};let r={};return t.querySelectorAll("input, select, textarea").forEach(e=>{e.name&&e.value&&(r[e.name]=e.value)}),r}function M(e){if("undefined"==typeof DOMParser)throw Error("DOMParser not available on server side");let t=new DOMParser().parseFromString(e,"text/html").querySelector("select[name=drpSemester]");if(!t)return null;let r=t.querySelectorAll("option"),n=[],s="";for(let e=0;e<r.length;e++){let t=r[e],a=t.innerHTML.split("_");n.push({value:t.value,from:a[1],to:a[2],th:a[0]}),t.selected&&(s=t.value)}return{semesters:n,currentSemester:s}}function H(e){let t,r;let n={lop_hoc_phan:"Lớp học phần",hoc_phan:"Học phần",thoi_gian:"Thời gian",dia_diem:"\xd0ịa điểm",giang_vien:"Giảng vi\xean",si_so:"Sĩ số",so_dk:"Số \xd0K",so_tc:"Số TC"};if(0==e.length||!1==e||(e.pop(),1==e.length))return!1;let s=e[0],a=e.slice(1,e.length),l=Array.prototype.map.call(a,function(e){let a="([0-9]{2}\\/[0-9]{2}\\/[0-9]{4}).+?([0-9]{2}\\/[0-9]{2}\\/[0-9]{4}):(\\([0-9]*\\))?(.+?)((Từ)|$)+?",l=RegExp(a,"g"),i=new RegExp(a),o=e[s.indexOf(n.dia_diem)],d=o.match(/\([0-9,]+?\)/g);d||(o=null),o&&(d.forEach(e=>o=o.replace(e,"\n"+e)),o=o.match(/\n\(([0-9,]+?)\)(.+)/g),(o=Array.prototype.map.call(o,e=>{let t=e.match(/\n\(([0-9,]+?)\)(.+)/);return t=[t[1].split(","),t[2]],Array.prototype.map.call(t[0],e=>"(".concat(e,") ").concat(t[1]))}).flat()).sort(function(e,t){return parseInt(e[1])-parseInt(t[1])}),o=Array.prototype.map.call(o,e=>e.replace(/^\([0-9]+?\) /i,"").trim()));let c=e[s.indexOf(n.thoi_gian)].match(l);return!!c&&(c.forEach((e,n)=>{if(!(e=e.match(i))){c.splice(n,1);return}e[4]=e[4].split("&nbsp;&nbsp;&nbsp;"),e[4].shift(),e[4].forEach((t,r)=>{if(!(t=t.match(/((Thứ .+?)||Chủ nhật) tiết (.+?)$/))){e[4].splice(r,1);return}t&&(t[3]=t[3].split(/[^0-9]+/g),t[3].pop(),t={dow:({"Thứ 2":2,"Thứ 3":3,"Thứ 4":4,"Thứ 5":5,"Thứ 6":6,"Thứ 7":7,"Chủ nhật":8})[t[1]],shi:t[3]}),e[4][r]=t}),e[1]="".concat(e[1].substr(3,2),"/").concat(e[1].substr(0,2),"/").concat(e[1].substr(6,4)),e[2]="".concat(e[2].substr(3,2),"/").concat(e[2].substr(0,2),"/").concat(e[2].substr(6,4)),e[1]=new Date(Date.parse(e[1])),e[2]=new Date(Date.parse(e[2])),e={startTime:e[1],endTime:e[2],dayOfWeek:e[4],address:o?o[n]:null},t?t>e.startTime&&(t=e.startTime):t=e.startTime,r?r<e.endTime&&(r=e.endTime):r=e.endTime,c[n]=e}),{lop_hoc_phan:e[s.indexOf(n.lop_hoc_phan)],hoc_phan:e[s.indexOf(n.hoc_phan)],giang_vien:e[s.indexOf(n.giang_vien)],si_so:e[s.indexOf(n.si_so)],so_dk:e[s.indexOf(n.so_dk)],so_tc:e[s.indexOf(n.so_tc)],tkb:c})});t=t.getTime(),r=r.getTime();let i=[];for(let e=t;e<=r;e+=864e5){if(new Date(e).getDay()+1==2||e==t){i.push([{time:e,shift:[]}]);continue}i[i.length-1].push({time:e,shift:[]})}for(let e of i)for(let t of e)t.shift=Array.from({length:16},(e,r)=>{for(let e of l)if(e){for(let n of e.tkb)if(t.time>=n.startTime.getTime()&&t.time<=n.endTime.getTime()){for(let s of n.dayOfWeek)if((s.dow==new Date(t.time).getDay()+1||new Date(t.time).getDay()+1==1&&8==s.dow)&&r+1>=parseInt(s.shi[0])&&r+1<=parseInt(s.shi[s.shi.length-1])){if(r+1===parseInt(s.shi[0]))return{content:"".concat(e.lop_hoc_phan).concat(n.address?" (học tại ".concat(n.address,")"):""),name:e.lop_hoc_phan,address:n.address?n.address:null,length:s.shi.length};return{content:null,name:null,address:null,length:0}}}}return{content:null,name:null,address:null,length:1}});return{data_subject:i}}var P=r(1549),q=r.n(P);async function L(e,t){let r=await fetch("https://actvn-schedule.cors-ngosangns.workers.dev/login",{method:"GET"}),n=await r.text(),s={__VIEWSTATE:C(n,"__VIEWSTATE"),__EVENTVALIDATION:C(n,"__EVENTVALIDATION"),txtUserName:e.toUpperCase(),txtPassword:q()(t),btnSubmit:"Đăng nhập"},a=(r=await fetch("https://actvn-schedule.cors-ngosangns.workers.dev/login",{method:"POST",body:Object.keys(s).map(e=>encodeURIComponent(e)+"="+encodeURIComponent(e in s?s[e]:"")).join("&"),headers:{"Content-Type":"application/x-www-form-urlencoded"}})).headers.get("set-cookie")||r.headers.get("Set-Cookie");if(a)return a;let l=await r.text();return l&&l.startsWith("SignIn="),l}function U(){window.localStorage.removeItem("calendar"),window.localStorage.removeItem("student"),window.localStorage.removeItem("semesters"),window.localStorage.removeItem("mainForm"),window.localStorage.removeItem("signInToken")}function F(){let e=(0,a.useRouter)(),[t,r]=(0,s.useState)(""),[l,i]=(0,s.useState)(""),[o,h]=(0,s.useState)(""),[f,g]=(0,s.useState)(!1),[v,b]=(0,s.useState)(""),[w,C]=(0,s.useState)(""),H=async r=>{r.preventDefault(),g(!0),b("");try{let r=await L(t,l),n=O(await E(r)),s=await D(n),a=R(n),i=A(n),o=M(n);I({signInToken:r,mainForm:i,semesters:o,calendar:s,student:a}),window.dispatchEvent(new CustomEvent("loginSuccess")),e.push("/calendar")}catch(e){console.error("Login error:",e),b("C\xf3 lỗi xảy ra khi lấy th\xf4ng tin thời kh\xf3a biểu hoặc t\xe0i khoản/mật khẩu kh\xf4ng đ\xfang!"),U()}finally{g(!1)}},P=async t=>{t.preventDefault(),C("");try{let t=O(o),r=await D(t),n=R(t),s=A(t),a=M(t);I({mainForm:s,semesters:a,calendar:r,student:n}),window.dispatchEvent(new CustomEvent("loginSuccess")),e.push("/calendar")}catch(e){console.error("User response processing error:",e),C("C\xf3 lỗi xảy ra khi lấy th\xf4ng tin thời kh\xf3a biểu!"),U()}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,n.jsx)("div",{className:"w-full max-w-6xl",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,n.jsxs)(d,{children:[(0,n.jsx)(c,{children:(0,n.jsx)(u,{className:"text-lg",children:"XEM THỜI KH\xd3A BIỂU TỪ T\xc0I KHOẢN TRƯỜNG"})}),(0,n.jsx)(m,{children:(0,n.jsxs)("form",{onSubmit:H,className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(y,{htmlFor:"username",children:"Username"}),(0,n.jsx)(x,{id:"username",type:"text",placeholder:"Username",value:t,onChange:e=>r(e.target.value),required:!0,disabled:f})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(y,{htmlFor:"password",children:"Password"}),(0,n.jsx)(x,{id:"password",type:"password",placeholder:"Password",value:l,onChange:e=>i(e.target.value),required:!0,disabled:f})]}),(0,n.jsxs)(p,{type:"submit",className:"w-full",disabled:f,children:[f&&(0,n.jsx)(_.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Đăng nhập"]}),v&&(0,n.jsxs)(j,{variant:"destructive",children:[(0,n.jsx)(k.Z,{className:"h-4 w-4"}),(0,n.jsx)(N,{children:v})]})]})})]}),(0,n.jsxs)(d,{className:"bg-secondary",children:[(0,n.jsx)(c,{children:(0,n.jsx)(u,{className:"text-lg",children:"XEM THỜI KH\xd3A BIỂU TỪ M\xc3 NGUỒN"})}),(0,n.jsxs)(m,{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Nhằm tăng t\xednh bảo mật cho t\xe0i khoản của c\xe1c bạn, web đ\xe3 hỗ trợ th\xeam t\xednh năng dịch thời kh\xf3a biểu từ m\xe3 nguồn HTML."}),(0,n.jsx)("p",{className:"text-sm font-medium mb-2",children:"Hướng dẫn:"}),(0,n.jsx)("div",{className:"bg-background rounded-lg p-4 mb-4",children:(0,n.jsxs)("ol",{className:"space-y-2 text-sm",children:[(0,n.jsxs)("li",{className:"flex items-start",children:[(0,n.jsx)("span",{className:"bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5",children:"1"}),(0,n.jsxs)("span",{children:["Bạn v\xe0o trang quản l\xed của trường tại địa chỉ"," ",(0,n.jsxs)("a",{href:"http://qldt.actvn.edu.vn",target:"_blank",rel:"noreferrer",className:"underline inline-flex items-center gap-1",children:["http://qldt.actvn.edu.vn",(0,n.jsx)(S.Z,{className:"w-3 h-3"})]})," ","v\xe0 tiến h\xe0nh đăng nhập."]})]}),(0,n.jsxs)("li",{className:"flex items-start",children:[(0,n.jsx)("span",{className:"bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5",children:"2"}),(0,n.jsxs)("span",{children:["V\xe0o mục: ",(0,n.jsx)("strong",{children:"Đăng k\xfd học"})," → ",(0,n.jsx)("strong",{children:"Xem kết quả ĐKH"}),"."]})]}),(0,n.jsxs)("li",{className:"flex items-start",children:[(0,n.jsx)("span",{className:"bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5",children:"3"}),(0,n.jsxs)("span",{children:["Chuột phải chọn ",(0,n.jsx)("strong",{children:"Xem m\xe3 nguồn (View page source)"})," v\xe0 copy to\xe0n bộ code."]})]}),(0,n.jsxs)("li",{className:"flex items-start",children:[(0,n.jsx)("span",{className:"bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5",children:"4"}),(0,n.jsxs)("span",{children:["D\xe1n code đ\xf3 v\xe0o \xf4 b\xean dưới v\xe0 bấm ",(0,n.jsx)("strong",{children:"Xem lịch học"}),"."]})]})]})}),(0,n.jsxs)("form",{onSubmit:P,className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(y,{htmlFor:"userResponse",children:"M\xe3 nguồn HTML"}),(0,n.jsx)(T,{id:"userResponse",placeholder:"D\xe1n m\xe3 nguồn của trang xem lịch học tại đ\xe2y...",value:o,onChange:e=>h(e.target.value),required:!0,disabled:f,rows:3})]}),(0,n.jsxs)(p,{type:"submit",className:"w-full",variant:"secondary",disabled:f,children:[f&&(0,n.jsx)(_.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Xem lịch học"]}),w&&(0,n.jsxs)(j,{variant:"destructive",children:[(0,n.jsx)(k.Z,{className:"h-4 w-4"}),(0,n.jsx)(N,{children:w})]})]})]})]})]})})})}}},function(e){e.O(0,[774,885,542,888,179],function(){return e(e.s=1814)}),_N_E=e.O()}]);