(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[640],{9477:function(e){var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}};e.exports=t},8410:function(e){var t,n;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|**********&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(e){for(var n=[],r=0;r<e.length;r+=3)for(var o=e[r]<<16|e[r+1]<<8|e[r+2],i=0;i<4;i++)8*r+6*i<=8*e.length?n.push(t.charAt(o>>>6*(3-i)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/ig,"");for(var n=[],r=0,o=0;r<e.length;o=++r%4)0!=o&&n.push((t.indexOf(e.charAt(r-1))&Math.pow(2,-2*o+8)-1)<<2*o|t.indexOf(e.charAt(r))>>>6-2*o);return n}},e.exports=n},9934:function(e){function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */e.exports=function(e){return null!=e&&(t(e)||"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))||!!e._isBuffer)}},9865:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},8203:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},2442:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3523:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},1291:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},7158:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},9224:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5817:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},1813:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},4689:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},6264:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},5883:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},6142:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4922:function(e,t,n){var r,o,i,l,a;r=n(8410),o=n(9477).utf8,i=n(9934),l=n(9477).bin,(a=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?l.stringToBytes(e):o.stringToBytes(e):i(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var n=r.bytesToWords(e),u=8*e.length,c=**********,s=-271733879,f=-**********,d=271733878,p=0;p<n.length;p++)n[p]=(n[p]<<8|n[p]>>>24)&16711935|(n[p]<<24|n[p]>>>8)&**********;n[u>>>5]|=128<<u%32,n[(u+64>>>9<<4)+14]=u;for(var h=a._ff,m=a._gg,v=a._hh,g=a._ii,p=0;p<n.length;p+=16){var y=c,w=s,x=f,b=d;c=h(c,s,f,d,n[p+0],7,-680876936),d=h(d,c,s,f,n[p+1],12,-389564586),f=h(f,d,c,s,n[p+2],17,606105819),s=h(s,f,d,c,n[p+3],22,-**********),c=h(c,s,f,d,n[p+4],7,-176418897),d=h(d,c,s,f,n[p+5],12,1200080426),f=h(f,d,c,s,n[p+6],17,-1473231341),s=h(s,f,d,c,n[p+7],22,-45705983),c=h(c,s,f,d,n[p+8],7,1770035416),d=h(d,c,s,f,n[p+9],12,-1958414417),f=h(f,d,c,s,n[p+10],17,-42063),s=h(s,f,d,c,n[p+11],22,-1990404162),c=h(c,s,f,d,n[p+12],7,1804603682),d=h(d,c,s,f,n[p+13],12,-40341101),f=h(f,d,c,s,n[p+14],17,-1502002290),s=h(s,f,d,c,n[p+15],22,1236535329),c=m(c,s,f,d,n[p+1],5,-165796510),d=m(d,c,s,f,n[p+6],9,-1069501632),f=m(f,d,c,s,n[p+11],14,643717713),s=m(s,f,d,c,n[p+0],20,-373897302),c=m(c,s,f,d,n[p+5],5,-701558691),d=m(d,c,s,f,n[p+10],9,38016083),f=m(f,d,c,s,n[p+15],14,-660478335),s=m(s,f,d,c,n[p+4],20,-405537848),c=m(c,s,f,d,n[p+9],5,568446438),d=m(d,c,s,f,n[p+14],9,-1019803690),f=m(f,d,c,s,n[p+3],14,-187363961),s=m(s,f,d,c,n[p+8],20,1163531501),c=m(c,s,f,d,n[p+13],5,-1444681467),d=m(d,c,s,f,n[p+2],9,-51403784),f=m(f,d,c,s,n[p+7],14,1735328473),s=m(s,f,d,c,n[p+12],20,-1926607734),c=v(c,s,f,d,n[p+5],4,-378558),d=v(d,c,s,f,n[p+8],11,-2022574463),f=v(f,d,c,s,n[p+11],16,1839030562),s=v(s,f,d,c,n[p+14],23,-35309556),c=v(c,s,f,d,n[p+1],4,-1530992060),d=v(d,c,s,f,n[p+4],11,1272893353),f=v(f,d,c,s,n[p+7],16,-155497632),s=v(s,f,d,c,n[p+10],23,-1094730640),c=v(c,s,f,d,n[p+13],4,681279174),d=v(d,c,s,f,n[p+0],11,-358537222),f=v(f,d,c,s,n[p+3],16,-722521979),s=v(s,f,d,c,n[p+6],23,76029189),c=v(c,s,f,d,n[p+9],4,-640364487),d=v(d,c,s,f,n[p+12],11,-421815835),f=v(f,d,c,s,n[p+15],16,530742520),s=v(s,f,d,c,n[p+2],23,-995338651),c=g(c,s,f,d,n[p+0],6,-198630844),d=g(d,c,s,f,n[p+7],10,1126891415),f=g(f,d,c,s,n[p+14],15,-1416354905),s=g(s,f,d,c,n[p+5],21,-57434055),c=g(c,s,f,d,n[p+12],6,1700485571),d=g(d,c,s,f,n[p+3],10,-1894986606),f=g(f,d,c,s,n[p+10],15,-1051523),s=g(s,f,d,c,n[p+1],21,-2054922799),c=g(c,s,f,d,n[p+8],6,1873313359),d=g(d,c,s,f,n[p+15],10,-30611744),f=g(f,d,c,s,n[p+6],15,-1560198380),s=g(s,f,d,c,n[p+13],21,1309151649),c=g(c,s,f,d,n[p+4],6,-145523070),d=g(d,c,s,f,n[p+11],10,-1120210379),f=g(f,d,c,s,n[p+2],15,718787259),s=g(s,f,d,c,n[p+9],21,-343485551),c=c+y>>>0,s=s+w>>>0,f=f+x>>>0,d=d+b>>>0}return r.endian([c,s,f,d])})._ff=function(e,t,n,r,o,i,l){var a=e+(t&n|~t&r)+(o>>>0)+l;return(a<<i|a>>>32-i)+t},a._gg=function(e,t,n,r,o,i,l){var a=e+(t&r|n&~r)+(o>>>0)+l;return(a<<i|a>>>32-i)+t},a._hh=function(e,t,n,r,o,i,l){var a=e+(t^n^r)+(o>>>0)+l;return(a<<i|a>>>32-i)+t},a._ii=function(e,t,n,r,o,i,l){var a=e+(n^(t|~r))+(o>>>0)+l;return(a<<i|a>>>32-i)+t},a._blocksize=16,a._digestsize=16,e.exports=function(e,t){if(null==e)throw Error("Illegal argument "+e);var n=r.wordsToBytes(a(e,t));return t&&t.asBytes?n:t&&t.asString?l.bytesToString(n):r.bytesToHex(n)}},5646:function(e,t,n){"use strict";let r;n.d(t,{VY:function(){return rc},ZA:function(){return rf},JO:function(){return ra},ck:function(){return rp},wU:function(){return rm},eT:function(){return rh},__:function(){return rd},h_:function(){return ru},fC:function(){return ro},$G:function(){return rg},u_:function(){return rv},Z0:function(){return ry},xz:function(){return ri},B4:function(){return rl},l_:function(){return rs}});var o,i,l,a,u,c,s=n(2265),f=n.t(s,2),d=n(4887);function p(e,[t,n]){return Math.min(n,Math.max(t,e))}var h=n(5744),m=n(7733),v=n(2210),g=n(6989),y=n(7437),w=s.createContext(void 0),x=n(9249),b=0;function S(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var E=n(9381),C=n(6459),k="focusScope.autoFocusOnMount",T="focusScope.autoFocusOnUnmount",R={bubbles:!1,cancelable:!0},A=s.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[a,u]=s.useState(null),c=(0,C.W)(o),f=(0,C.W)(i),d=s.useRef(null),p=(0,v.e)(t,e=>u(e)),h=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(r){let e=function(e){if(h.paused||!a)return;let t=e.target;a.contains(t)?d.current=t:P(d.current,{select:!0})},t=function(e){if(h.paused||!a)return;let t=e.relatedTarget;null===t||a.contains(t)||P(d.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&P(a)});return a&&n.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,a,h.paused]),s.useEffect(()=>{if(a){j.add(h);let e=document.activeElement;if(!a.contains(e)){let t=new CustomEvent(k,R);a.addEventListener(k,c),a.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(P(r,{select:t}),document.activeElement!==n)return}(M(a).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&P(a))}return()=>{a.removeEventListener(k,c),setTimeout(()=>{let t=new CustomEvent(T,R);a.addEventListener(T,f),a.dispatchEvent(t),t.defaultPrevented||P(e??document.body,{select:!0}),a.removeEventListener(T,f),j.remove(h)},0)}}},[a,c,f,h]);let m=s.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=M(e);return[L(t,e),L(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&P(i,{select:!0})):(e.preventDefault(),n&&P(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,y.jsx)(E.WV.div,{tabIndex:-1,...l,ref:p,onKeyDown:m})});function M(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function L(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function P(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}A.displayName="FocusScope";var j=(r=[],{add(e){let t=r[0];e!==t&&t?.pause(),(r=N(r,e)).unshift(e)},remove(e){r=N(r,e),r[0]?.resume()}});function N(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var O=n(1030),I=f[" useId ".trim().toString()]||(()=>void 0),D=0;function W(e){let[t,n]=s.useState(I());return(0,O.b)(()=>{e||n(e=>e??String(D++))},[e]),e||(t?`radix-${t}`:"")}let B=["top","right","bottom","left"],H=Math.min,_=Math.max,F=Math.round,V=Math.floor,Z=e=>({x:e,y:e}),z={left:"right",right:"left",bottom:"top",top:"bottom"},K={start:"end",end:"start"};function U(e,t){return"function"==typeof e?e(t):e}function $(e){return e.split("-")[0]}function q(e){return e.split("-")[1]}function Y(e){return"x"===e?"y":"x"}function X(e){return"y"===e?"height":"width"}let G=new Set(["top","bottom"]);function J(e){return G.has($(e))?"y":"x"}function Q(e){return e.replace(/start|end/g,e=>K[e])}let ee=["left","right"],et=["right","left"],en=["top","bottom"],er=["bottom","top"];function eo(e){return e.replace(/left|right|bottom|top/g,e=>z[e])}function ei(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function el(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ea(e,t,n){let r,{reference:o,floating:i}=e,l=J(t),a=Y(J(t)),u=X(a),c=$(t),s="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(q(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let eu=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=ea(c,r,u),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=ea(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function ec(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=U(t,e),h=ei(p),m=a[d?"floating"===f?"reference":"floating":f],v=el(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=el(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function es(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ef(e){return B.some(t=>e[t]>=0)}let ed=new Set(["left","top"]);async function ep(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=$(n),a=q(n),u="y"===J(n),c=ed.has(l)?-1:1,s=i&&u?-1:1,f=U(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:d*c}:{x:d*c,y:p*s}}function eh(){return"undefined"!=typeof window}function em(e){return ey(e)?(e.nodeName||"").toLowerCase():"#document"}function ev(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eg(e){var t;return null==(t=(ey(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ey(e){return!!eh()&&(e instanceof Node||e instanceof ev(e).Node)}function ew(e){return!!eh()&&(e instanceof Element||e instanceof ev(e).Element)}function ex(e){return!!eh()&&(e instanceof HTMLElement||e instanceof ev(e).HTMLElement)}function eb(e){return!!eh()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ev(e).ShadowRoot)}let eS=new Set(["inline","contents"]);function eE(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eO(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!eS.has(o)}let eC=new Set(["table","td","th"]),ek=[":popover-open",":modal"];function eT(e){return ek.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eR=["transform","translate","scale","rotate","perspective"],eA=["transform","translate","scale","rotate","perspective","filter"],eM=["paint","layout","strict","content"];function eL(e){let t=eP(),n=ew(e)?eO(e):e;return eR.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eA.some(e=>(n.willChange||"").includes(e))||eM.some(e=>(n.contain||"").includes(e))}function eP(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ej=new Set(["html","body","#document"]);function eN(e){return ej.has(em(e))}function eO(e){return ev(e).getComputedStyle(e)}function eI(e){return ew(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eD(e){if("html"===em(e))return e;let t=e.assignedSlot||e.parentNode||eb(e)&&e.host||eg(e);return eb(t)?t.host:t}function eW(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eD(t);return eN(n)?t.ownerDocument?t.ownerDocument.body:t.body:ex(n)&&eE(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=ev(o);if(i){let e=eB(l);return t.concat(l,l.visualViewport||[],eE(o)?o:[],e&&n?eW(e):[])}return t.concat(o,eW(o,[],n))}function eB(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eH(e){let t=eO(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=ex(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=F(n)!==i||F(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function e_(e){return ew(e)?e:e.contextElement}function eF(e){let t=e_(e);if(!ex(t))return Z(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eH(t),l=(i?F(n.width):n.width)/r,a=(i?F(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eV=Z(0);function eZ(e){let t=ev(e);return eP()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eV}function ez(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=e_(e),a=Z(1);t&&(r?ew(r)&&(a=eF(r)):a=eF(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ev(l))&&o)?eZ(l):Z(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,f=i.width/a.x,d=i.height/a.y;if(l){let e=ev(l),t=r&&ew(r)?ev(r):r,n=e,o=eB(n);for(;o&&r&&t!==n;){let e=eF(o),t=o.getBoundingClientRect(),r=eO(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,f*=e.x,d*=e.y,c+=i,s+=l,o=eB(n=ev(o))}}return el({width:f,height:d,x:c,y:s})}function eK(e,t){let n=eI(e).scrollLeft;return t?t.left+n:ez(eg(e)).left+n}function eU(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eK(e,r)),y:r.top+t.scrollTop}}let e$=new Set(["absolute","fixed"]);function eq(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ev(e),r=eg(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eP();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=eg(e),n=eI(e),r=e.ownerDocument.body,o=_(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=_(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eK(e),a=-n.scrollTop;return"rtl"===eO(r).direction&&(l+=_(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(eg(e));else if(ew(t))r=function(e,t){let n=ez(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ex(e)?eF(e):Z(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eZ(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return el(r)}function eY(e){return"static"===eO(e).position}function eX(e,t){if(!ex(e)||"fixed"===eO(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eg(e)===n&&(n=n.ownerDocument.body),n}function eG(e,t){var n;let r=ev(e);if(eT(e))return r;if(!ex(e)){let t=eD(e);for(;t&&!eN(t);){if(ew(t)&&!eY(t))return t;t=eD(t)}return r}let o=eX(e,t);for(;o&&(n=o,eC.has(em(n)))&&eY(o);)o=eX(o,t);return o&&eN(o)&&eY(o)&&!eL(o)?r:o||function(e){let t=eD(e);for(;ex(t)&&!eN(t);){if(eL(t))return t;if(eT(t))break;t=eD(t)}return null}(e)||r}let eJ=async function(e){let t=this.getOffsetParent||eG,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=ex(t),o=eg(t),i="fixed"===n,l=ez(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=Z(0);if(r||!r&&!i){if(("body"!==em(t)||eE(o))&&(a=eI(t)),r){let e=ez(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eK(o))}i&&!r&&o&&(u.x=eK(o));let c=!o||r||i?Z(0):eU(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eQ={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=eg(r),a=!!t&&eT(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=Z(1),s=Z(0),f=ex(r);if((f||!f&&!i)&&(("body"!==em(r)||eE(l))&&(u=eI(r)),ex(r))){let e=ez(r);c=eF(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let d=!l||f||i?Z(0):eU(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+d.x,y:n.y*c.y-u.scrollTop*c.y+s.y+d.y}},getDocumentElement:eg,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eT(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eW(e,[],!1).filter(e=>ew(e)&&"body"!==em(e)),o=null,i="fixed"===eO(e).position,l=i?eD(e):e;for(;ew(l)&&!eN(l);){let t=eO(l),n=eL(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&e$.has(o.position)||eE(l)&&!n&&function e(t,n){let r=eD(t);return!(r===n||!ew(r)||eN(r))&&("fixed"===eO(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eD(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eq(t,n,o);return e.top=_(r.top,e.top),e.right=H(r.right,e.right),e.bottom=H(r.bottom,e.bottom),e.left=_(r.left,e.left),e},eq(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eG,getElementRects:eJ,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eH(e);return{width:t,height:n}},getScale:eF,isElement:ew,isRTL:function(e){return"rtl"===eO(e).direction}};function e0(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e1=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=U(e,t)||{};if(null==c)return{};let f=ei(s),d={x:n,y:r},p=Y(J(o)),h=X(p),m=await l.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-d[p]-i.floating[h],w=d[p]-i.reference[p],x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),b=x?x[g]:0;b&&await (null==l.isElement?void 0:l.isElement(x))||(b=a.floating[g]||i.floating[h]);let S=b/2-m[h]/2-1,E=H(f[v?"top":"left"],S),C=H(f[v?"bottom":"right"],S),k=b-m[h]-C,T=b/2-m[h]/2+(y/2-w/2),R=_(E,H(T,k)),A=!u.arrow&&null!=q(o)&&T!==R&&i.reference[h]/2-(T<E?E:C)-m[h]/2<0,M=A?T<E?T-E:T-k:0;return{[p]:d[p]+M,data:{[p]:R,centerOffset:T-R-M,...A&&{alignmentOffset:M}},reset:A}}}),e2=(e,t,n)=>{let r=new Map,o={platform:eQ,...n},i={...o.platform,_c:r};return eu(e,t,{...o,platform:i})};var e3="undefined"!=typeof document?s.useLayoutEffect:function(){};function e8(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e8(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e8(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e5(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e6(e,t){let n=e5(e);return Math.round(t*n)/n}function e4(e){let t=s.useRef(e);return e3(()=>{t.current=e}),t}let e9=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e1({element:n.current,padding:r}).fn(t):{}:n?e1({element:n,padding:r}).fn(t):{}}}),e7=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:l,middlewareData:a}=e,u=await ep(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}),options:[e,t]}},te=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=U(n,e),c={x:t,y:r},s=await ec(e,u),f=J($(o)),d=Y(f),p=c[d],h=c[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+s[e],r=p-s[t];p=_(n,H(p,r))}if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+s[e],r=h-s[t];h=_(n,H(h,r))}let m=a.fn({...e,[d]:p,[f]:h});return{...m,data:{x:m.x-t,y:m.y-r,enabled:{[d]:i,[f]:l}}}}}),options:[e,t]}},tt=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:l}=e,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=U(n,e),s={x:t,y:r},f=J(o),d=Y(f),p=s[d],h=s[f],m=U(a,e),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+v.mainAxis,n=i.reference[d]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===d?"width":"height",t=ed.has($(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[f])||0)+(t?0:v.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[f])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}),options:[e,t]}},tn=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:f,elements:d}=e,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=U(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let x=$(a),b=J(s),S=$(s)===s,E=await (null==f.isRTL?void 0:f.isRTL(d.floating)),C=m||(S||!y?[eo(s)]:function(e){let t=eo(e);return[Q(e),t,Q(t)]}(s)),k="none"!==g;!m&&k&&C.push(...function(e,t,n,r){let o=q(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?et:ee;return t?ee:et;case"left":case"right":return t?en:er;default:return[]}}($(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(Q)))),i}(s,y,g,E));let T=[s,...C],R=await ec(e,w),A=[],M=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&A.push(R[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=q(e),o=Y(J(e)),i=X(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=eo(l)),[l,eo(l)]}(a,c,E);A.push(R[e[0]],R[e[1]])}if(M=[...M,{placement:a,overflows:A}],!A.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=T[e];if(t&&(!("alignment"===h&&b!==J(t))||M.every(e=>e.overflows[0]>0&&J(e.placement)===b)))return{data:{index:e,overflows:M},reset:{placement:t}};let n=null==(i=M.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(l=M.filter(e=>{if(k){let t=J(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},tr=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=e,{apply:s=()=>{},...f}=U(n,e),d=await ec(e,f),p=$(l),h=q(l),m="y"===J(l),{width:v,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-d.top-d.bottom,w=v-d.left-d.right,x=H(g-d[o],y),b=H(v-d[i],w),S=!e.middlewareData.shift,E=x,C=b;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(C=w),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(E=y),S&&!h){let e=_(d.left,0),t=_(d.right,0),n=_(d.top,0),r=_(d.bottom,0);m?C=v-2*(0!==e||0!==t?e+t:_(d.left,d.right)):E=g-2*(0!==n||0!==r?n+r:_(d.top,d.bottom))}await s({...e,availableWidth:C,availableHeight:E});let k=await u.getDimensions(c.floating);return v!==k.width||g!==k.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},to=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=U(n,e);switch(r){case"referenceHidden":{let n=es(await ec(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:ef(n)}}}case"escaped":{let n=es(await ec(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:ef(n)}}}default:return{}}}}),options:[e,t]}},ti=(e,t)=>({...e9(e),options:[e,t]});var tl=s.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,y.jsx)(E.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,y.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tl.displayName="Arrow";var ta="Popper",[tu,tc]=(0,g.b)(ta),[ts,tf]=tu(ta),td=e=>{let{__scopePopper:t,children:n}=e,[r,o]=s.useState(null);return(0,y.jsx)(ts,{scope:t,anchor:r,onAnchorChange:o,children:n})};td.displayName=ta;var tp="PopperAnchor",th=s.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=tf(tp,n),l=s.useRef(null),a=(0,v.e)(t,l);return s.useEffect(()=>{i.onAnchorChange(r?.current||l.current)}),r?null:(0,y.jsx)(E.WV.div,{...o,ref:a})});th.displayName=tp;var tm="PopperContent",[tv,tg]=tu(tm),ty=s.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:l=0,arrowPadding:a=0,avoidCollisions:u=!0,collisionBoundary:c=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:h=!1,updatePositionStrategy:m="optimized",onPlaced:g,...w}=e,x=tf(tm,n),[b,S]=s.useState(null),k=(0,v.e)(t,e=>S(e)),[T,R]=s.useState(null),A=function(e){let[t,n]=s.useState(void 0);return(0,O.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(T),M=A?.width??0,L=A?.height??0,P="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},j=Array.isArray(c)?c:[c],N=j.length>0,I={padding:P,boundary:j.filter(tS),altBoundary:N},{refs:D,floatingStyles:W,placement:B,isPositioned:F,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:a=!0,whileElementsMounted:u,open:c}=e,[f,p]=s.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=s.useState(r);e8(h,r)||m(r);let[v,g]=s.useState(null),[y,w]=s.useState(null),x=s.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),b=s.useCallback(e=>{e!==k.current&&(k.current=e,w(e))},[]),S=i||v,E=l||y,C=s.useRef(null),k=s.useRef(null),T=s.useRef(f),R=null!=u,A=e4(u),M=e4(o),L=e4(c),P=s.useCallback(()=>{if(!C.current||!k.current)return;let e={placement:t,strategy:n,middleware:h};M.current&&(e.platform=M.current),e2(C.current,k.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};j.current&&!e8(T.current,t)&&(T.current=t,d.flushSync(()=>{p(t)}))})},[h,t,n,M,L]);e3(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[c]);let j=s.useRef(!1);e3(()=>(j.current=!0,()=>{j.current=!1}),[]),e3(()=>{if(S&&(C.current=S),E&&(k.current=E),S&&E){if(A.current)return A.current(S,E,P);P()}},[S,E,P,A,R]);let N=s.useMemo(()=>({reference:C,floating:k,setReference:x,setFloating:b}),[x,b]),O=s.useMemo(()=>({reference:S,floating:E}),[S,E]),I=s.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=e6(O.floating,f.x),r=e6(O.floating,f.y);return a?{...e,transform:"translate("+t+"px, "+r+"px)",...e5(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,a,O.floating,f.x,f.y]);return s.useMemo(()=>({...f,update:P,refs:N,elements:O,floatingStyles:I}),[f,P,N,O,I])}({strategy:"fixed",placement:r+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=e_(e),f=i||l?[...s?eW(s):[],...eW(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let d=s&&u?function(e,t){let n,r=null,o=eg(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:f,width:d,height:p}=c;if(a||t(),!d||!p)return;let h=V(f),m=V(o.clientWidth-(s+d)),v={rootMargin:-h+"px "+-m+"px "+-V(o.clientHeight-(f+p))+"px "+-V(s)+"px",threshold:_(0,H(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||e0(c,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?ez(e):null;return c&&function t(){let r=ez(e);m&&!e0(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===m}),elements:{reference:x.anchor},middleware:[e7({mainAxis:o+L,alignmentAxis:l}),u&&te({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?tt():void 0,...I}),u&&tn({...I}),tr({...I,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),T&&ti({element:T,padding:a}),tE({arrowWidth:M,arrowHeight:L}),h&&to({strategy:"referenceHidden",...I})]}),[z,K]=tC(B),U=(0,C.W)(g);(0,O.b)(()=>{F&&U?.()},[F,U]);let $=Z.arrow?.x,q=Z.arrow?.y,Y=Z.arrow?.centerOffset!==0,[X,G]=s.useState();return(0,O.b)(()=>{b&&G(window.getComputedStyle(b).zIndex)},[b]),(0,y.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:F?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[Z.transformOrigin?.x,Z.transformOrigin?.y].join(" "),...Z.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,y.jsx)(tv,{scope:n,placedSide:z,onArrowChange:R,arrowX:$,arrowY:q,shouldHideArrow:Y,children:(0,y.jsx)(E.WV.div,{"data-side":z,"data-align":K,...w,ref:k,style:{...w.style,animation:F?void 0:"none"}})})})});ty.displayName=tm;var tw="PopperArrow",tx={top:"bottom",right:"left",bottom:"top",left:"right"},tb=s.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tg(tw,n),i=tx[o.placedSide];return(0,y.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,y.jsx)(tl,{...r,ref:t,style:{...r.style,display:"block"}})})});function tS(e){return null!==e}tb.displayName=tw;var tE=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,c]=tC(n),s={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===u?(p=i?s:`${f}px`,h=`${-a}px`):"top"===u?(p=i?s:`${f}px`,h=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,h=i?s:`${d}px`):"left"===u&&(p=`${r.floating.width+a}px`,h=i?s:`${d}px`),{data:{x:p,y:h}}}});function tC(e){let[t,n="center"]=e.split("-");return[t,n]}var tk=n(2730),tT=n(7256),tR=n(3763),tA=n(8281),tM=new WeakMap,tL=new WeakMap,tP={},tj=0,tN=function(e){return e&&(e.host||tN(e.parentNode))},tO=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tN(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tP[n]||(tP[n]=new WeakMap);var i=tP[n],l=[],a=new Set,u=new Set(o),c=function(e){!e||a.has(e)||(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tM.get(e)||0)+1,c=(i.get(e)||0)+1;tM.set(e,u),i.set(e,c),l.push(e),1===u&&o&&tL.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tj++,function(){l.forEach(function(e){var t=tM.get(e)-1,o=i.get(e)-1;tM.set(e,t),i.set(e,o),t||(tL.has(e)||e.removeAttribute(r),tL.delete(e)),o||e.removeAttribute(n)}),--tj||(tM=new WeakMap,tM=new WeakMap,tL=new WeakMap,tP={})}},tI=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tO(r,o,n,"aria-hidden")):function(){return null}},tD=function(){return(tD=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tW(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var tB="right-scroll-bar-position",tH="width-before-scroll-bar";function t_(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tF="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,tV=new WeakMap,tZ=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),l=[],a=!1,u={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:null},useMedium:function(e){var t=i(e,a);return l.push(t),function(){l=l.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;l.length;){var t=l;l=[],t.forEach(e)}l={push:function(t){return e(t)},filter:function(){return l}}},assignMedium:function(e){a=!0;var t=[];if(l.length){var n=l;l=[],n.forEach(e),t=l}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),l={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),l}}}}).options=tD({async:!0,ssr:!1},o),u),tz=function(){},tK=s.forwardRef(function(e,t){var n,r,o,i,l=s.useRef(null),a=s.useState({onScrollCapture:tz,onWheelCapture:tz,onTouchMoveCapture:tz}),u=a[0],c=a[1],f=e.forwardProps,d=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,x=e.inert,b=e.allowPinchZoom,S=e.as,E=e.gapMode,C=tW(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(n=[l,t],r=function(e){return n.forEach(function(t){return t_(t,e)})},(o=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tF(function(){var e=tV.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||t_(e,null)}),r.forEach(function(e){t.has(e)||t_(e,o)})}tV.set(i,n)},[n]),i),T=tD(tD({},C),u);return s.createElement(s.Fragment,null,m&&s.createElement(g,{sideCar:tZ,removeScrollBar:h,shards:v,noRelative:y,noIsolation:w,inert:x,setCallbacks:c,allowPinchZoom:!!b,lockRef:l,gapMode:E}),f?s.cloneElement(s.Children.only(d),tD(tD({},T),{ref:k})):s.createElement(void 0===S?"div":S,tD({},T,{className:p,ref:k}),d))});tK.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tK.classNames={fullWidth:tH,zeroRight:tB};var tU=function(e){var t=e.sideCar,n=tW(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,tD({},n))};tU.isSideCarExport=!0;var t$=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=c||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tq=function(){var e=t$();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tY=function(){var e=tq();return function(t){return e(t.styles,t.dynamic),null}},tX={left:0,top:0,right:0,gap:0},tG=function(e){return parseInt(e||"",10)||0},tJ=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tG(n),tG(r),tG(o)]},tQ=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tX;var t=tJ(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},t0=tY(),t1="data-scroll-locked",t2=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(t1,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tB," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tH," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tB," .").concat(tB," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tH," .").concat(tH," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t1,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},t3=function(){var e=parseInt(document.body.getAttribute(t1)||"0",10);return isFinite(e)?e:0},t8=function(){s.useEffect(function(){return document.body.setAttribute(t1,(t3()+1).toString()),function(){var e=t3()-1;e<=0?document.body.removeAttribute(t1):document.body.setAttribute(t1,e.toString())}},[])},t5=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t8();var i=s.useMemo(function(){return tQ(o)},[o]);return s.createElement(t0,{styles:t2(i,!t,o,n?"":"!important")})},t6=!1;if("undefined"!=typeof window)try{var t4=Object.defineProperty({},"passive",{get:function(){return t6=!0,!0}});window.addEventListener("test",t4,t4),window.removeEventListener("test",t4,t4)}catch(e){t6=!1}var t9=!!t6&&{passive:!1},t7=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},ne=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nt(e,r)){var o=nn(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nt=function(e,t){return"v"===e?t7(t,"overflowY"):t7(t,"overflowX")},nn=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nr=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,f=a>0,d=0,p=0;do{if(!u)break;var h=nn(e,u),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&nt(e,u)&&(d+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&a>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},no=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ni=function(e){return[e.deltaX,e.deltaY]},nl=function(e){return e&&"current"in e?e.current:e},na=0,nu=[],nc=(tZ.useMedium(function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),o=s.useState(na++)[0],i=s.useState(tY)[0],l=s.useRef(e);s.useEffect(function(){l.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nl),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=no(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=ne(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=ne(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nr(p,t,e,"h"===p?u:c,!0)},[]),u=s.useCallback(function(e){if(nu.length&&nu[nu.length-1]===i){var n="deltaY"in e?ni(e):no(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(nl).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?a(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=s.useCallback(function(e){n.current=no(e),r.current=void 0},[]),d=s.useCallback(function(t){c(t.type,ni(t),t.target,a(t,e.lockRef.current))},[]),p=s.useCallback(function(t){c(t.type,no(t),t.target,a(t,e.lockRef.current))},[]);s.useEffect(function(){return nu.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,t9),document.addEventListener("touchmove",u,t9),document.addEventListener("touchstart",f,t9),function(){nu=nu.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,t9),document.removeEventListener("touchmove",u,t9),document.removeEventListener("touchstart",f,t9)}},[]);var h=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?s.createElement(t5,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),tU),ns=s.forwardRef(function(e,t){return s.createElement(tK,tD({},e,{ref:t,sideCar:nc}))});ns.classNames=tK.classNames;var nf=[" ","Enter","ArrowUp","ArrowDown"],nd=[" ","Enter"],np="Select",[nh,nm,nv]=(0,m.B)(np),[ng,ny]=(0,g.b)(np,[nv,tc]),nw=tc(),[nx,nb]=ng(np),[nS,nE]=ng(np),nC=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:l,defaultValue:a,onValueChange:u,dir:c,name:f,autoComplete:d,disabled:p,required:h,form:m}=e,v=nw(t),[g,x]=s.useState(null),[b,S]=s.useState(null),[E,C]=s.useState(!1),k=function(e){let t=s.useContext(w);return e||t||"ltr"}(c),[T,R]=(0,tR.T)({prop:r,defaultProp:o??!1,onChange:i,caller:np}),[A,M]=(0,tR.T)({prop:l,defaultProp:a,onChange:u,caller:np}),L=s.useRef(null),P=!g||m||!!g.closest("form"),[j,N]=s.useState(new Set),O=Array.from(j).map(e=>e.props.value).join(";");return(0,y.jsx)(td,{...v,children:(0,y.jsxs)(nx,{required:h,scope:t,trigger:g,onTriggerChange:x,valueNode:b,onValueNodeChange:S,valueNodeHasChildren:E,onValueNodeHasChildrenChange:C,contentId:W(),value:A,onValueChange:M,open:T,onOpenChange:R,dir:k,triggerPointerDownPosRef:L,disabled:p,children:[(0,y.jsx)(nh.Provider,{scope:t,children:(0,y.jsx)(nS,{scope:e.__scopeSelect,onNativeOptionAdd:s.useCallback(e=>{N(t=>new Set(t).add(e))},[]),onNativeOptionRemove:s.useCallback(e=>{N(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),P?(0,y.jsxs)(re,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:d,value:A,onChange:e=>M(e.target.value),disabled:p,form:m,children:[void 0===A?(0,y.jsx)("option",{value:""}):null,Array.from(j)]},O):null]})})};nC.displayName=np;var nk="SelectTrigger",nT=s.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=nw(n),l=nb(nk,n),a=l.disabled||r,u=(0,v.e)(t,l.onTriggerChange),c=nm(n),f=s.useRef("touch"),[d,p,m]=rn(e=>{let t=c().filter(e=>!e.disabled),n=t.find(e=>e.value===l.value),r=rr(t,e,n);void 0!==r&&l.onValueChange(r.value)}),g=e=>{a||(l.onOpenChange(!0),m()),e&&(l.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,y.jsx)(th,{asChild:!0,...i,children:(0,y.jsx)(E.WV.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":rt(l.value)?"":void 0,...o,ref:u,onClick:(0,h.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&g(e)}),onPointerDown:(0,h.M)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,h.M)(o.onKeyDown,e=>{let t=""!==d.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),(!t||" "!==e.key)&&nf.includes(e.key)&&(g(),e.preventDefault())})})})});nT.displayName=nk;var nR="SelectValue",nA=s.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nb(nR,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,f=(0,v.e)(t,u.onValueNodeChange);return(0,O.b)(()=>{c(s)},[c,s]),(0,y.jsx)(E.WV.span,{...a,ref:f,style:{pointerEvents:"none"},children:rt(u.value)?(0,y.jsx)(y.Fragment,{children:l}):i})});nA.displayName=nR;var nM=s.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,y.jsx)(E.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nM.displayName="SelectIcon";var nL=e=>(0,y.jsx)(tk.h,{asChild:!0,...e});nL.displayName="SelectPortal";var nP="SelectContent",nj=s.forwardRef((e,t)=>{let n=nb(nP,e.__scopeSelect),[r,o]=s.useState();return((0,O.b)(()=>{o(new DocumentFragment)},[]),n.open)?(0,y.jsx)(nD,{...e,ref:t}):r?d.createPortal((0,y.jsx)(nN,{scope:e.__scopeSelect,children:(0,y.jsx)(nh.Slot,{scope:e.__scopeSelect,children:(0,y.jsx)("div",{children:e.children})})}),r):null});nj.displayName=nP;var[nN,nO]=ng(nP),nI=(0,tT.Z8)("SelectContent.RemoveScroll"),nD=s.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:a,sideOffset:u,align:c,alignOffset:f,arrowPadding:d,collisionBoundary:p,collisionPadding:m,sticky:g,hideWhenDetached:w,avoidCollisions:E,...C}=e,k=nb(nP,n),[T,R]=s.useState(null),[M,L]=s.useState(null),P=(0,v.e)(t,e=>R(e)),[j,N]=s.useState(null),[O,I]=s.useState(null),D=nm(n),[W,B]=s.useState(!1),H=s.useRef(!1);s.useEffect(()=>{if(T)return tI(T)},[T]),s.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??S()),document.body.insertAdjacentElement("beforeend",e[1]??S()),b++,()=>{1===b&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),b--}},[]);let _=s.useCallback(e=>{let[t,...n]=D().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&M&&(M.scrollTop=0),n===r&&M&&(M.scrollTop=M.scrollHeight),n?.focus(),document.activeElement!==o))return},[D,M]),F=s.useCallback(()=>_([j,T]),[_,j,T]);s.useEffect(()=>{W&&F()},[W,F]);let{onOpenChange:V,triggerPointerDownPosRef:Z}=k;s.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(Z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(Z.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),Z.current=null};return null!==Z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,V,Z]),s.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[z,K]=rn(e=>{let t=D().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=rr(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),U=s.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==k.value&&k.value===t||r)&&(N(e),r&&(H.current=!0))},[k.value]),$=s.useCallback(()=>T?.focus(),[T]),q=s.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==k.value&&k.value===t||r)&&I(e)},[k.value]),Y="popper"===r?nB:nW,X=Y===nB?{side:a,sideOffset:u,align:c,alignOffset:f,arrowPadding:d,collisionBoundary:p,collisionPadding:m,sticky:g,hideWhenDetached:w,avoidCollisions:E}:{};return(0,y.jsx)(nN,{scope:n,content:T,viewport:M,onViewportChange:L,itemRefCallback:U,selectedItem:j,onItemLeave:$,itemTextRefCallback:q,focusSelectedItem:F,selectedItemText:O,position:r,isPositioned:W,searchRef:z,children:(0,y.jsx)(ns,{as:nI,allowPinchZoom:!0,children:(0,y.jsx)(A,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,h.M)(o,e=>{k.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,y.jsx)(x.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,y.jsx)(Y,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...C,...X,onPlaced:()=>B(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,h.M)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||K(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=D().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>_(t)),e.preventDefault()}})})})})})})});nD.displayName="SelectContentImpl";var nW=s.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nb(nP,n),l=nO(nP,n),[a,u]=s.useState(null),[c,f]=s.useState(null),d=(0,v.e)(t,e=>f(e)),h=nm(n),m=s.useRef(!1),g=s.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:b,focusSelectedItem:S}=l,C=s.useCallback(()=>{if(i.trigger&&i.valueNode&&a&&c&&w&&x&&b){let e=i.trigger.getBoundingClientRect(),t=c.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,u=e.width+l,c=Math.max(u,t.width),s=p(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.left=s+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,u=e.width+l,c=Math.max(u,t.width),s=p(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.right=s+"px"}let l=h(),u=window.innerHeight-20,s=w.scrollHeight,f=window.getComputedStyle(c),d=parseInt(f.borderTopWidth,10),v=parseInt(f.paddingTop,10),g=parseInt(f.borderBottomWidth,10),y=d+v+s+parseInt(f.paddingBottom,10)+g,S=Math.min(5*x.offsetHeight,y),E=window.getComputedStyle(w),C=parseInt(E.paddingTop,10),k=parseInt(E.paddingBottom,10),T=e.top+e.height/2-10,R=x.offsetHeight/2,A=d+v+(x.offsetTop+R);if(A<=T){let e=l.length>0&&x===l[l.length-1].ref.current;a.style.bottom="0px";let t=c.clientHeight-w.offsetTop-w.offsetHeight;a.style.height=A+Math.max(u-T,R+(e?k:0)+t+g)+"px"}else{let e=l.length>0&&x===l[0].ref.current;a.style.top="0px";let t=Math.max(T,d+w.offsetTop+(e?C:0)+R);a.style.height=t+(y-A)+"px",w.scrollTop=A-T+w.offsetTop}a.style.margin="10px 0",a.style.minHeight=S+"px",a.style.maxHeight=u+"px",r?.(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,a,c,w,x,b,i.dir,r]);(0,O.b)(()=>C(),[C]);let[k,T]=s.useState();(0,O.b)(()=>{c&&T(window.getComputedStyle(c).zIndex)},[c]);let R=s.useCallback(e=>{e&&!0===g.current&&(C(),S?.(),g.current=!1)},[C,S]);return(0,y.jsx)(nH,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,y.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,y.jsx)(E.WV.div,{...o,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nW.displayName="SelectItemAlignedPosition";var nB=s.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nw(n);return(0,y.jsx)(ty,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nB.displayName="SelectPopperPosition";var[nH,n_]=ng(nP,{}),nF="SelectViewport",nV=s.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nO(nF,n),l=n_(nF,n),a=(0,v.e)(t,i.onViewportChange),u=s.useRef(0);return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,y.jsx)(nh.Slot,{scope:n,children:(0,y.jsx)(E.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,h.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=l;if(r?.current&&n){let e=Math.abs(u.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});nV.displayName=nF;var nZ="SelectGroup",[nz,nK]=ng(nZ),nU=s.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=W();return(0,y.jsx)(nz,{scope:n,id:o,children:(0,y.jsx)(E.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})});nU.displayName=nZ;var n$="SelectLabel",nq=s.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nK(n$,n);return(0,y.jsx)(E.WV.div,{id:o.id,...r,ref:t})});nq.displayName=n$;var nY="SelectItem",[nX,nG]=ng(nY),nJ=s.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...l}=e,a=nb(nY,n),u=nO(nY,n),c=a.value===r,[f,d]=s.useState(i??""),[p,m]=s.useState(!1),g=(0,v.e)(t,e=>u.itemRefCallback?.(e,r,o)),w=W(),x=s.useRef("touch"),b=()=>{o||(a.onValueChange(r),a.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,y.jsx)(nX,{scope:n,value:r,disabled:o,textId:w,isSelected:c,onItemTextChange:s.useCallback(e=>{d(t=>t||(e?.textContent??"").trim())},[]),children:(0,y.jsx)(nh.ItemSlot,{scope:n,value:r,disabled:o,textValue:f,children:(0,y.jsx)(E.WV.div,{role:"option","aria-labelledby":w,"data-highlighted":p?"":void 0,"aria-selected":c&&p,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...l,ref:g,onFocus:(0,h.M)(l.onFocus,()=>m(!0)),onBlur:(0,h.M)(l.onBlur,()=>m(!1)),onClick:(0,h.M)(l.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,h.M)(l.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,h.M)(l.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,h.M)(l.onPointerMove,e=>{x.current=e.pointerType,o?u.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,h.M)(l.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:(0,h.M)(l.onKeyDown,e=>{u.searchRef?.current!==""&&" "===e.key||(nd.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});nJ.displayName=nY;var nQ="SelectItemText",n0=s.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,l=nb(nQ,n),a=nO(nQ,n),u=nG(nQ,n),c=nE(nQ,n),[f,p]=s.useState(null),h=(0,v.e)(t,e=>p(e),u.onItemTextChange,e=>a.itemTextRefCallback?.(e,u.value,u.disabled)),m=f?.textContent,g=s.useMemo(()=>(0,y.jsx)("option",{value:u.value,disabled:u.disabled,children:m},u.value),[u.disabled,u.value,m]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=c;return(0,O.b)(()=>(w(g),()=>x(g)),[w,x,g]),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(E.WV.span,{id:u.textId,...i,ref:h}),u.isSelected&&l.valueNode&&!l.valueNodeHasChildren?d.createPortal(i.children,l.valueNode):null]})});n0.displayName=nQ;var n1="SelectItemIndicator",n2=s.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nG(n1,n).isSelected?(0,y.jsx)(E.WV.span,{"aria-hidden":!0,...r,ref:t}):null});n2.displayName=n1;var n3="SelectScrollUpButton",n8=s.forwardRef((e,t)=>{let n=nO(n3,e.__scopeSelect),r=n_(n3,e.__scopeSelect),[o,i]=s.useState(!1),l=(0,v.e)(t,r.onScrollButtonChange);return(0,O.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,y.jsx)(n4,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});n8.displayName=n3;var n5="SelectScrollDownButton",n6=s.forwardRef((e,t)=>{let n=nO(n5,e.__scopeSelect),r=n_(n5,e.__scopeSelect),[o,i]=s.useState(!1),l=(0,v.e)(t,r.onScrollButtonChange);return(0,O.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,y.jsx)(n4,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});n6.displayName=n5;var n4=s.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nO("SelectScrollButton",n),l=s.useRef(null),a=nm(n),u=s.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return s.useEffect(()=>()=>u(),[u]),(0,O.b)(()=>{let e=a().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[a]),(0,y.jsx)(E.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,h.M)(o.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(r,50))}),onPointerMove:(0,h.M)(o.onPointerMove,()=>{i.onItemLeave?.(),null===l.current&&(l.current=window.setInterval(r,50))}),onPointerLeave:(0,h.M)(o.onPointerLeave,()=>{u()})})}),n9=s.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,y.jsx)(E.WV.div,{"aria-hidden":!0,...r,ref:t})});n9.displayName="SelectSeparator";var n7="SelectArrow";s.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nw(n),i=nb(n7,n),l=nO(n7,n);return i.open&&"popper"===l.position?(0,y.jsx)(tb,{...o,...r,ref:t}):null}).displayName=n7;var re=s.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{let o=s.useRef(null),i=(0,v.e)(r,o),l=function(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return s.useEffect(()=>{let e=o.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(l!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[l,t]),(0,y.jsx)(E.WV.select,{...n,style:{...tA.C2,...n.style},ref:i,defaultValue:t})});function rt(e){return""===e||void 0===e}function rn(e){let t=(0,C.W)(e),n=s.useRef(""),r=s.useRef(0),o=s.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=s.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return s.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function rr(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}re.displayName="SelectBubbleInput";var ro=nC,ri=nT,rl=nA,ra=nM,ru=nL,rc=nj,rs=nV,rf=nU,rd=nq,rp=nJ,rh=n0,rm=n2,rv=n8,rg=n6,ry=n9}}]);