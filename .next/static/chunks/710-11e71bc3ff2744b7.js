"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[710],{575:function(e,t,n){n.d(t,{z:function(){return d}});var r=n(7437),a=n(2265),o=n(7256),s=n(6061),u=n(2169);let i=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:n,variant:a,size:s,asChild:d=!1,...l}=e,c=d?o.g7:"button";return(0,r.jsx)(c,{className:(0,u.cn)(i({variant:a,size:s,className:n})),ref:t,...l})});d.displayName="Button"},5671:function(e,t,n){n.d(t,{Ol:function(){return u},SZ:function(){return d},Zb:function(){return s},aY:function(){return l},ll:function(){return i}});var r=n(7437),a=n(2265),o=n(2169);let s=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",n),...a})});s.displayName="Card";let u=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",n),...a})});u.displayName="CardHeader";let i=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",n),...a})});i.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",n),...a})});d.displayName="CardDescription";let l=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",n),...a})});l.displayName="CardContent",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",n),...a})}).displayName="CardFooter"},5300:function(e,t,n){n.r(t),n.d(t,{AppProvider:function(){return d},useApp:function(){return l},useAuth:function(){return c},useCalendar:function(){return f},useUI:function(){return p}});var r=n(7437),a=n(2265),o=n(8104);let s={auth:{user:null,isAuthenticated:!1,isLoading:!1,error:null},calendar:null,ui:{theme:"dark",sidebarOpen:!1,currentView:"calendar"},student:null};function u(e,t){switch(t.type){case"AUTH_START":return{...e,auth:{...e.auth,isLoading:!0,error:null}};case"AUTH_SUCCESS":return{...e,auth:{user:t.payload.user,isAuthenticated:!0,isLoading:!1,error:null}};case"AUTH_ERROR":return{...e,auth:{user:null,isAuthenticated:!1,isLoading:!1,error:t.payload}};case"AUTH_LOGOUT":return{...e,auth:{user:null,isAuthenticated:!1,isLoading:!1,error:null},calendar:null,student:null};case"SET_CALENDAR":return{...e,calendar:t.payload};case"SET_STUDENT":return{...e,student:t.payload};case"SET_THEME":return{...e,ui:{...e.ui,theme:t.payload}};case"TOGGLE_SIDEBAR":return{...e,ui:{...e.ui,sidebarOpen:!e.ui.sidebarOpen}};case"SET_VIEW":return{...e,ui:{...e.ui,currentView:t.payload}};case"LOAD_FROM_STORAGE":let{signInToken:n,calendar:r,student:a,user:o}=t.payload;return{...e,auth:{user:o||null,isAuthenticated:!!(n||r),isLoading:!1,error:null},calendar:r||null,student:a||null};default:return e}}let i=(0,a.createContext)(null);function d(e){let{children:t}=e,[n,d]=(0,a.useReducer)(u,s);return(0,a.useEffect)(()=>{let e=(0,o.mu)();e&&d({type:"LOAD_FROM_STORAGE",payload:e})},[]),(0,a.useEffect)(()=>{if(n.auth.isAuthenticated&&n.calendar){let e={calendar:n.calendar,student:n.student||void 0,user:n.auth.user||void 0};(0,o.OH)(e)}},[n.auth.isAuthenticated,n.calendar,n.student,n.auth.user]),(0,r.jsx)(i.Provider,{value:{state:n,dispatch:d},children:t})}function l(){let e=(0,a.useContext)(i);if(!e)throw Error("useApp must be used within an AppProvider");return e}function c(){let{state:e,dispatch:t}=l();return{...e.auth,login:(e,n)=>t({type:"AUTH_SUCCESS",payload:{user:e,signInToken:n}}),logout:()=>t({type:"AUTH_LOGOUT"}),setLoading:()=>t({type:"AUTH_START"}),setError:e=>t({type:"AUTH_ERROR",payload:e})}}function f(){let{state:e,dispatch:t}=l();return{calendar:e.calendar,student:e.student,setCalendar:e=>t({type:"SET_CALENDAR",payload:e}),setStudent:e=>t({type:"SET_STUDENT",payload:e})}}function p(){let{state:e,dispatch:t}=l();return{...e.ui,setTheme:e=>t({type:"SET_THEME",payload:e}),toggleSidebar:()=>t({type:"TOGGLE_SIDEBAR"}),setView:e=>t({type:"SET_VIEW",payload:e})}}},6862:function(e,t,n){n.d(t,{pm:function(){return f}});var r=n(2265);let a=0,o=new Map,s=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},u=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:n}=t;return n?s(n):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],d={toasts:[]};function l(e){d=u(d,e),i.forEach(e=>{e(d)})}function c(e){let{...t}=e,n=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>l({type:"DISMISS_TOAST",toastId:n});return l({type:"ADD_TOAST",toast:{...t,id:n,open:!0,onOpenChange:e=>{e||r()}}}),{id:n,dismiss:r,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:n}})}}function f(){let[e,t]=r.useState(d);return r.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},8104:function(e,t,n){function r(e){e.semesters&&window.localStorage.setItem("semesters",JSON.stringify(e.semesters)),e.signInToken&&e.signInToken.length&&window.localStorage.setItem("signInToken",e.signInToken),e.mainForm&&window.localStorage.setItem("mainForm",JSON.stringify(e.mainForm)),e.calendar&&window.localStorage.setItem("calendar",JSON.stringify(e.calendar)),e.student&&window.localStorage.setItem("student",e.student)}function a(){let e=window.localStorage.getItem("calendar"),t=window.localStorage.getItem("student"),n=window.localStorage.getItem("semesters"),r=window.localStorage.getItem("mainForm"),a=window.localStorage.getItem("signInToken");return{calendar:e?JSON.parse(e):null,student:t||null,semesters:n?JSON.parse(n):null,mainForm:r?JSON.parse(r):null,signInToken:a||null}}function o(){window.localStorage.removeItem("calendar"),window.localStorage.removeItem("student"),window.localStorage.removeItem("semesters"),window.localStorage.removeItem("mainForm"),window.localStorage.removeItem("signInToken")}n.d(t,{Nk:function(){return o},OH:function(){return r},mu:function(){return a}})},2169:function(e,t,n){n.d(t,{N8:function(){return l},UZ:function(){return f},cn:function(){return u},e$:function(){return d},kJ:function(){return c},p6:function(){return i}});var r=n(7042),a=n(4769),o=n(2067),s=n.n(o);function u(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.m6)((0,r.W)(t))}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"DD/MM/YYYY";return s()(e).format(t)}function d(e){return e instanceof Error?e.message:"string"==typeof e?e:"Đ\xe3 xảy ra lỗi kh\xf4ng x\xe1c định"}function l(e){return({1:{start:"07:00",end:"07:50"},2:{start:"08:00",end:"08:50"},3:{start:"09:00",end:"09:50"},4:{start:"10:00",end:"10:50"},5:{start:"11:00",end:"11:50"},6:{start:"12:00",end:"12:50"},7:{start:"13:00",end:"13:50"},8:{start:"14:00",end:"14:50"},9:{start:"15:00",end:"15:50"},10:{start:"16:00",end:"16:50"},11:{start:"17:00",end:"17:50"},12:{start:"18:00",end:"18:50"},13:{start:"19:00",end:"19:50"},14:{start:"20:00",end:"20:50"},15:{start:"21:00",end:"21:50"}})[e]||{start:"00:00",end:"00:00"}}function c(e){return e>=1&&e<=6?"morning":e>=7&&e<=12?"afternoon":"evening"}function f(e){return["Chủ nhật","Thứ hai","Thứ ba","Thứ tư","Thứ năm","Thứ s\xe1u","Thứ bảy"][e]||"Kh\xf4ng x\xe1c định"}}}]);