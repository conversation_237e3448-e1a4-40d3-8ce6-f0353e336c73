(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[542],{487:function(e){var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],r=0;r<e.length;r++)t.push(255&e.charCodeAt(r));return t},bytesToString:function(e){for(var t=[],r=0;r<e.length;r++)t.push(String.fromCharCode(e[r]));return t.join("")}}};e.exports=t},1012:function(e){var t,r;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&r.rotl(e,8)|**********&r.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=r.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],r=0,o=0;r<e.length;r++,o+=8)t[o>>>5]|=e[r]<<24-o%32;return t},wordsToBytes:function(e){for(var t=[],r=0;r<32*e.length;r+=8)t.push(e[r>>>5]>>>24-r%32&255);return t},bytesToHex:function(e){for(var t=[],r=0;r<e.length;r++)t.push((e[r]>>>4).toString(16)),t.push((15&e[r]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},bytesToBase64:function(e){for(var r=[],o=0;o<e.length;o+=3)for(var n=e[o]<<16|e[o+1]<<8|e[o+2],l=0;l<4;l++)8*o+6*l<=8*e.length?r.push(t.charAt(n>>>6*(3-l)&63)):r.push("=");return r.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/ig,"");for(var r=[],o=0,n=0;o<e.length;n=++o%4)0!=n&&r.push((t.indexOf(e.charAt(o-1))&Math.pow(2,-2*n+8)-1)<<2*n|t.indexOf(e.charAt(o))>>>6-2*n);return r}},e.exports=r},8738:function(e){function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */e.exports=function(e){return null!=e&&(t(e)||"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))||!!e._isBuffer)}},4297:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var o=r(7294),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let r=(0,o.forwardRef)(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:u="",children:c,...d},f)=>(0,o.createElement)("svg",{ref:f,...n,width:i,height:i,stroke:r,strokeWidth:a?24*Number(s)/Number(i):s,className:["lucide",`lucide-${l(e)}`,u].join(" "),...d},[...t.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r}},7653:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,r(4297).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8814:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,r(4297).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},3217:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,r(4297).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1549:function(e,t,r){var o,n,l,i,s;o=r(1012),n=r(487).utf8,l=r(8738),i=r(487).bin,(s=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?i.stringToBytes(e):n.stringToBytes(e):l(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var r=o.bytesToWords(e),a=8*e.length,u=**********,c=-271733879,d=-**********,f=271733878,p=0;p<r.length;p++)r[p]=(r[p]<<8|r[p]>>>24)&16711935|(r[p]<<24|r[p]>>>8)&**********;r[a>>>5]|=128<<a%32,r[(a+64>>>9<<4)+14]=a;for(var b=s._ff,g=s._gg,m=s._hh,h=s._ii,p=0;p<r.length;p+=16){var y=u,v=c,x=d,w=f;u=b(u,c,d,f,r[p+0],7,-680876936),f=b(f,u,c,d,r[p+1],12,-389564586),d=b(d,f,u,c,r[p+2],17,606105819),c=b(c,d,f,u,r[p+3],22,-**********),u=b(u,c,d,f,r[p+4],7,-176418897),f=b(f,u,c,d,r[p+5],12,**********),d=b(d,f,u,c,r[p+6],17,-**********),c=b(c,d,f,u,r[p+7],22,-45705983),u=b(u,c,d,f,r[p+8],7,1770035416),f=b(f,u,c,d,r[p+9],12,-1958414417),d=b(d,f,u,c,r[p+10],17,-42063),c=b(c,d,f,u,r[p+11],22,-1990404162),u=b(u,c,d,f,r[p+12],7,1804603682),f=b(f,u,c,d,r[p+13],12,-40341101),d=b(d,f,u,c,r[p+14],17,-1502002290),c=b(c,d,f,u,r[p+15],22,1236535329),u=g(u,c,d,f,r[p+1],5,-165796510),f=g(f,u,c,d,r[p+6],9,-1069501632),d=g(d,f,u,c,r[p+11],14,643717713),c=g(c,d,f,u,r[p+0],20,-373897302),u=g(u,c,d,f,r[p+5],5,-701558691),f=g(f,u,c,d,r[p+10],9,38016083),d=g(d,f,u,c,r[p+15],14,-660478335),c=g(c,d,f,u,r[p+4],20,-405537848),u=g(u,c,d,f,r[p+9],5,568446438),f=g(f,u,c,d,r[p+14],9,-1019803690),d=g(d,f,u,c,r[p+3],14,-187363961),c=g(c,d,f,u,r[p+8],20,1163531501),u=g(u,c,d,f,r[p+13],5,-1444681467),f=g(f,u,c,d,r[p+2],9,-51403784),d=g(d,f,u,c,r[p+7],14,1735328473),c=g(c,d,f,u,r[p+12],20,-1926607734),u=m(u,c,d,f,r[p+5],4,-378558),f=m(f,u,c,d,r[p+8],11,-2022574463),d=m(d,f,u,c,r[p+11],16,1839030562),c=m(c,d,f,u,r[p+14],23,-35309556),u=m(u,c,d,f,r[p+1],4,-1530992060),f=m(f,u,c,d,r[p+4],11,1272893353),d=m(d,f,u,c,r[p+7],16,-155497632),c=m(c,d,f,u,r[p+10],23,-1094730640),u=m(u,c,d,f,r[p+13],4,681279174),f=m(f,u,c,d,r[p+0],11,-358537222),d=m(d,f,u,c,r[p+3],16,-722521979),c=m(c,d,f,u,r[p+6],23,76029189),u=m(u,c,d,f,r[p+9],4,-640364487),f=m(f,u,c,d,r[p+12],11,-421815835),d=m(d,f,u,c,r[p+15],16,530742520),c=m(c,d,f,u,r[p+2],23,-995338651),u=h(u,c,d,f,r[p+0],6,-198630844),f=h(f,u,c,d,r[p+7],10,1126891415),d=h(d,f,u,c,r[p+14],15,-1416354905),c=h(c,d,f,u,r[p+5],21,-57434055),u=h(u,c,d,f,r[p+12],6,1700485571),f=h(f,u,c,d,r[p+3],10,-1894986606),d=h(d,f,u,c,r[p+10],15,-1051523),c=h(c,d,f,u,r[p+1],21,-2054922799),u=h(u,c,d,f,r[p+8],6,1873313359),f=h(f,u,c,d,r[p+15],10,-30611744),d=h(d,f,u,c,r[p+6],15,-1560198380),c=h(c,d,f,u,r[p+13],21,1309151649),u=h(u,c,d,f,r[p+4],6,-145523070),f=h(f,u,c,d,r[p+11],10,-1120210379),d=h(d,f,u,c,r[p+2],15,718787259),c=h(c,d,f,u,r[p+9],21,-343485551),u=u+y>>>0,c=c+v>>>0,d=d+x>>>0,f=f+w>>>0}return o.endian([u,c,d,f])})._ff=function(e,t,r,o,n,l,i){var s=e+(t&r|~t&o)+(n>>>0)+i;return(s<<l|s>>>32-l)+t},s._gg=function(e,t,r,o,n,l,i){var s=e+(t&o|r&~o)+(n>>>0)+i;return(s<<l|s>>>32-l)+t},s._hh=function(e,t,r,o,n,l,i){var s=e+(t^r^o)+(n>>>0)+i;return(s<<l|s>>>32-l)+t},s._ii=function(e,t,r,o,n,l,i){var s=e+(r^(t|~o))+(n>>>0)+i;return(s<<l|s>>>32-l)+t},s._blocksize=16,s._digestsize=16,e.exports=function(e,t){if(null==e)throw Error("Illegal argument "+e);var r=o.wordsToBytes(s(e,t));return t&&t.asBytes?r:t&&t.asString?i.bytesToString(r):o.bytesToHex(r)}},658:function(e,t,r){"use strict";function o(e){}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clientHookInServerComponentError",{enumerable:!0,get:function(){return o}}),r(8754),r(7294),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},227:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return p},useSearchParams:function(){return b},usePathname:function(){return g},ServerInsertedHTMLContext:function(){return a.ServerInsertedHTMLContext},useServerInsertedHTML:function(){return a.useServerInsertedHTML},useRouter:function(){return m},useParams:function(){return h},useSelectedLayoutSegments:function(){return y},useSelectedLayoutSegment:function(){return v},redirect:function(){return u.redirect},permanentRedirect:function(){return u.permanentRedirect},RedirectType:function(){return u.RedirectType},notFound:function(){return c.notFound}});let o=r(7294),n=r(3360),l=r(7164),i=r(658),s=r(3775),a=r(4402),u=r(4608),c=r(119),d=Symbol("internal for urlsearchparams readonly");function f(){return Error("ReadonlyURLSearchParams cannot be modified")}class p{[Symbol.iterator](){return this[d][Symbol.iterator]()}append(){throw f()}delete(){throw f()}set(){throw f()}sort(){throw f()}constructor(e){this[d]=e,this.entries=e.entries.bind(e),this.forEach=e.forEach.bind(e),this.get=e.get.bind(e),this.getAll=e.getAll.bind(e),this.has=e.has.bind(e),this.keys=e.keys.bind(e),this.values=e.values.bind(e),this.toString=e.toString.bind(e),this.size=e.size}}function b(){(0,i.clientHookInServerComponentError)("useSearchParams");let e=(0,o.useContext)(l.SearchParamsContext);return(0,o.useMemo)(()=>e?new p(e):null,[e])}function g(){return(0,i.clientHookInServerComponentError)("usePathname"),(0,o.useContext)(l.PathnameContext)}function m(){(0,i.clientHookInServerComponentError)("useRouter");let e=(0,o.useContext)(n.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function h(){(0,i.clientHookInServerComponentError)("useParams");let e=(0,o.useContext)(n.GlobalLayoutRouterContext),t=(0,o.useContext)(l.PathParamsContext);return(0,o.useMemo)(()=>(null==e?void 0:e.tree)?function e(t,r){for(let o of(void 0===r&&(r={}),Object.values(t[1]))){let t=o[0],n=Array.isArray(t),l=n?t[1]:t;!l||l.startsWith("__PAGE__")||(n&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):n&&(r[t[0]]=t[1]),r=e(o,r))}return r}(e.tree):t,[null==e?void 0:e.tree,t])}function y(e){void 0===e&&(e="children"),(0,i.clientHookInServerComponentError)("useSelectedLayoutSegments");let{tree:t}=(0,o.useContext)(n.LayoutRouterContext);return function e(t,r,o,n){let l;if(void 0===o&&(o=!0),void 0===n&&(n=[]),o)l=t[1][r];else{var i;let e=t[1];l=null!=(i=e.children)?i:Object.values(e)[0]}if(!l)return n;let a=l[0],u=(0,s.getSegmentValue)(a);return!u||u.startsWith("__PAGE__")?n:(n.push(u),e(l,r,!1,n))}(t,e)}function v(e){void 0===e&&(e="children"),(0,i.clientHookInServerComponentError)("useSelectedLayoutSegment");let t=y(e);return 0===t.length?null:t[0]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},119:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{notFound:function(){return o},isNotFoundError:function(){return n}});let r="NEXT_NOT_FOUND";function o(){let e=Error(r);throw e.digest=r,e}function n(e){return(null==e?void 0:e.digest)===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6154:function(e,t){"use strict";var r,o;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),(o=r||(r={}))[o.SeeOther=303]="SeeOther",o[o.TemporaryRedirect=307]="TemporaryRedirect",o[o.PermanentRedirect=308]="PermanentRedirect",("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4608:function(e,t,r){"use strict";var o,n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return o},getRedirectError:function(){return u},redirect:function(){return c},permanentRedirect:function(){return d},isRedirectError:function(){return f},getURLFromRedirectError:function(){return p},getRedirectTypeFromError:function(){return b},getRedirectStatusCodeFromError:function(){return g}});let l=r(377),i=r(2568),s=r(6154),a="NEXT_REDIRECT";function u(e,t,r){void 0===r&&(r=s.RedirectStatusCode.TemporaryRedirect);let o=Error(a);o.digest=a+";"+t+";"+e+";"+r+";";let n=l.requestAsyncStorage.getStore();return n&&(o.mutableCookies=n.mutableCookies),o}function c(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?s.RedirectStatusCode.SeeOther:s.RedirectStatusCode.PermanentRedirect)}function f(e){if("string"!=typeof(null==e?void 0:e.digest))return!1;let[t,r,o,n]=e.digest.split(";",4),l=Number(n);return t===a&&("replace"===r||"push"===r)&&"string"==typeof o&&!isNaN(l)&&l in s.RedirectStatusCode}function p(e){return f(e)?e.digest.split(";",3)[2]:null}function b(e){if(!f(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function g(e){if(!f(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(n=o||(o={})).push="push",n.replace="replace",("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3775:function(e,t){"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4402:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ServerInsertedHTMLContext:function(){return n},useServerInsertedHTML:function(){return l}});let o=r(1757)._(r(7294)),n=o.default.createContext(null);function l(e){let t=(0,o.useContext)(n);t&&t(e)}},9332:function(e,t,r){e.exports=r(227)},2568:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"actionAsyncStorage",{enumerable:!0,get:function(){return o}});let o=(0,r(1704).createAsyncLocalStorage)();("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1704:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createAsyncLocalStorage",{enumerable:!0,get:function(){return l}});let r=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class o{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}}let n=globalThis.AsyncLocalStorage;function l(){return n?new n:new o}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},377:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"requestAsyncStorage",{enumerable:!0,get:function(){return o}});let o=(0,r(1704).createAsyncLocalStorage)();("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3440:function(e,t,r){"use strict";r.d(t,{f:function(){return a}});var o=r(7294);r(3935);var n=r(1465),l=r(5893),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.Z8)(`Primitive.${t}`),i=o.forwardRef((e,o)=>{let{asChild:n,...i}=e,s=n?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s,{...i,ref:o})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),s=o.forwardRef((e,t)=>(0,l.jsx)(i.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var a=s},1465:function(e,t,r){"use strict";r.d(t,{g7:function(){return s},Z8:function(){return i}});var o=r(7294);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var l=r(5893);function i(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:r,...l}=e;if(o.isValidElement(r)){let e,i;let s=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,a=function(e,t){let r={...t};for(let o in t){let n=e[o],l=t[o];/^on[A-Z]/.test(o)?n&&l?r[o]=(...e)=>{let t=l(...e);return n(...e),t}:n&&(r[o]=n):"style"===o?r[o]={...n,...l}:"className"===o&&(r[o]=[n,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==o.Fragment&&(a.ref=t?function(...e){return t=>{let r=!1,o=e.map(e=>{let o=n(e,t);return r||"function"!=typeof o||(r=!0),o});if(r)return()=>{for(let t=0;t<o.length;t++){let r=o[t];"function"==typeof r?r():n(e[t],null)}}}}(t,s):s),o.cloneElement(r,a)}return o.Children.count(r)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=o.forwardRef((e,r)=>{let{children:n,...i}=e,s=o.Children.toArray(n),a=s.find(u);if(a){let e=a.props.children,n=s.map(t=>t!==a?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,l.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var s=i("Slot"),a=Symbol("radix.slottable");function u(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},2003:function(e,t,r){"use strict";r.d(t,{j:function(){return i}});var o=r(512);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=o.W,i=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:s}=t,a=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],o=null==s?void 0:s[e];if(null===t)return null;let l=n(t)||n(o);return i[e][l]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return l(e,a,null==t?void 0:null===(o=t.compoundVariants)||void 0===o?void 0:o.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},512:function(e,t,r){"use strict";function o(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,o,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(o=e(t[r]))&&(n&&(n+=" "),n+=o)}else for(o in t)t[o]&&(n&&(n+=" "),n+=o)}return n}(e))&&(o&&(o+=" "),o+=t);return o}r.d(t,{W:function(){return o}})},8388:function(e,t,r){"use strict";r.d(t,{m6:function(){return X}});let o=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||i(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&o[e]?[...n,...o[e]]:n}}},n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),l=o?n(e.slice(1),o):void 0;if(l)return l;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},l=/^\[(.+)\]$/,i=e=>{if(l.test(e)){let t=l.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{a(r,o,e,t)}),o},a=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e){if(c(e)){a(e(o),t,r,o);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,n])=>{a(n,u(t,e),r,o)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,n=(n,l)=>{r.set(n,l),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,o=1===t.length,n=t[0],l=t.length,i=e=>{let r;let i=[],s=0,a=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===s){if(c===n&&(o||e.slice(u,u+l)===t)){i.push(e.slice(a,u)),a=u+l;continue}if("/"===c){r=u;continue}}"["===c?s++:"]"===c&&s--}let u=0===i.length?e:e.substring(a),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:i,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};return r?e=>r({className:e,parseClassName:i}):i},b=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},g=e=>({cache:f(e.cacheSize),parseClassName:p(e),...o(e)}),m=/\s+/,h=(e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n}=t,l=[],i=e.trim().split(m),s="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{modifiers:a,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,p=o(f?c.substring(0,d):c);if(!p){if(!f||!(p=o(c))){s=t+(s.length>0?" "+s:s);continue}f=!1}let g=b(a).join(":"),m=u?g+"!":g,h=m+p;if(l.includes(h))continue;l.push(h);let y=n(p,f);for(let e=0;e<y.length;++e){let t=y[e];l.push(m+t)}s=t+(s.length>0?" "+s:s)}return s};function y(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=v(e))&&(o&&(o+=" "),o+=t);return o}let v=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=v(e[o]))&&(r&&(r+=" "),r+=t);return r},x=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,_=/^\d+\/\d+$/,j=new Set(["px","full","screen"]),k=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,C=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,P=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,O=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,R=e=>z(e)||j.has(e)||_.test(e),M=e=>F(e,"length",V),z=e=>!!e&&!Number.isNaN(Number(e)),E=e=>F(e,"number",z),A=e=>!!e&&Number.isInteger(Number(e)),T=e=>e.endsWith("%")&&z(e.slice(0,-1)),N=e=>w.test(e),I=e=>k.test(e),L=new Set(["length","size","percentage"]),B=e=>F(e,L,q),W=e=>F(e,"position",q),H=new Set(["image","url"]),$=e=>F(e,H,U),G=e=>F(e,"",D),Z=()=>!0,F=(e,t,r)=>{let o=w.exec(e);return!!o&&(o[1]?"string"==typeof t?o[1]===t:t.has(o[1]):r(o[2]))},V=e=>S.test(e)&&!C.test(e),q=()=>!1,D=e=>P.test(e),U=e=>O.test(e),X=function(e){let t,r,o;let n=function(i){return r=(t=g([].reduce((e,t)=>t(e),e()))).cache.get,o=t.cache.set,n=l,l(i)};function l(e){let n=r(e);if(n)return n;let l=h(e,t);return o(e,l),l}return function(){return n(y.apply(null,arguments))}}(()=>{let e=x("colors"),t=x("spacing"),r=x("blur"),o=x("brightness"),n=x("borderColor"),l=x("borderRadius"),i=x("borderSpacing"),s=x("borderWidth"),a=x("contrast"),u=x("grayscale"),c=x("hueRotate"),d=x("invert"),f=x("gap"),p=x("gradientColorStops"),b=x("gradientColorStopPositions"),g=x("inset"),m=x("margin"),h=x("opacity"),y=x("padding"),v=x("saturate"),w=x("scale"),_=x("sepia"),j=x("skew"),k=x("space"),S=x("translate"),C=()=>["auto","contain","none"],P=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto",N,t],L=()=>[N,t],H=()=>["",R,M],F=()=>["auto",z,N],V=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],q=()=>["solid","dashed","dotted","double","none"],D=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],U=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",N],J=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>[z,N];return{cacheSize:500,separator:":",theme:{colors:[Z],spacing:[R,M],blur:["none","",I,N],brightness:K(),borderColor:[e],borderRadius:["none","","full",I,N],borderSpacing:L(),borderWidth:H(),contrast:K(),grayscale:X(),hueRotate:K(),invert:X(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[T,M],inset:O(),margin:O(),opacity:K(),padding:L(),saturate:K(),scale:K(),sepia:X(),skew:K(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[I]}],"break-after":[{"break-after":J()}],"break-before":[{"break-before":J()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...V(),N]}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:C()}],"overscroll-x":[{"overscroll-x":C()}],"overscroll-y":[{"overscroll-y":C()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",A,N]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",A,N]}],"grid-cols":[{"grid-cols":[Z]}],"col-start-end":[{col:["auto",{span:["full",A,N]},N]}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":[Z]}],"row-start-end":[{row:["auto",{span:[A,N]},N]}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...U()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...U(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...U(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[m]}],mx:[{mx:[m]}],my:[{my:[m]}],ms:[{ms:[m]}],me:[{me:[m]}],mt:[{mt:[m]}],mr:[{mr:[m]}],mb:[{mb:[m]}],ml:[{ml:[m]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",N,t]}],"min-w":[{"min-w":[N,t,"min","max","fit"]}],"max-w":[{"max-w":[N,t,"none","full","min","max","fit","prose",{screen:[I]},I]}],h:[{h:[N,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[N,t,"auto","min","max","fit"]}],"font-size":[{text:["base",I,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",E]}],"font-family":[{font:[Z]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",z,E]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",R,N]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",R,M]}],"underline-offset":[{"underline-offset":["auto",R,N]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...V(),W]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",B]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},$]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[b]}],"gradient-via-pos":[{via:[b]}],"gradient-to-pos":[{to:[b]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...q(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:q()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...q()]}],"outline-offset":[{"outline-offset":[R,N]}],"outline-w":[{outline:[R,M]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:H()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[R,M]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",I,G]}],"shadow-color":[{shadow:[Z]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...D(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":D()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",I,N]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[v]}],sepia:[{sepia:[_]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:K()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:K()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[A,N]}],"translate-x":[{"translate-x":[S]}],"translate-y":[{"translate-y":[S]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[R,M,E]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);