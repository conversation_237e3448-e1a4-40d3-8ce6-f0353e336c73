import { renderHook, act } from '@testing-library/react'
import { useToast } from '@/hooks/use-toast'

describe('useToast', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with empty toasts', () => {
    const { result } = renderHook(() => useToast())
    
    expect(result.current.toasts).toEqual([])
  })

  it('should add a toast', () => {
    const { result } = renderHook(() => useToast())
    
    act(() => {
      result.current.toast({
        title: 'Test Toast',
        description: 'Test Description',
      })
    })

    expect(result.current.toasts).toHaveLength(1)
    expect(result.current.toasts[0]).toMatchObject({
      title: 'Test Toast',
      description: 'Test Description',
    })
    expect(result.current.toasts[0].id).toBeDefined()
  })

  it('should add multiple toasts', () => {
    const { result } = renderHook(() => useToast())
    
    act(() => {
      result.current.toast({ title: 'Toast 1' })
      result.current.toast({ title: 'Toast 2' })
    })

    expect(result.current.toasts).toHaveLength(2)
    expect(result.current.toasts[0].title).toBe('Toast 1')
    expect(result.current.toasts[1].title).toBe('Toast 2')
  })

  it('should respect toast limit', () => {
    const { result } = renderHook(() => useToast())
    
    act(() => {
      // Add more toasts than the limit (TOAST_LIMIT = 1)
      result.current.toast({ title: 'Toast 1' })
      result.current.toast({ title: 'Toast 2' })
    })

    // Should only keep the most recent toast due to limit
    expect(result.current.toasts).toHaveLength(1)
    expect(result.current.toasts[0].title).toBe('Toast 2')
  })

  it('should dismiss a toast', () => {
    const { result } = renderHook(() => useToast())
    
    let toastId: string
    
    act(() => {
      result.current.toast({ title: 'Test Toast' })
      toastId = result.current.toasts[0].id
    })

    expect(result.current.toasts).toHaveLength(1)

    act(() => {
      result.current.dismiss(toastId)
    })

    expect(result.current.toasts).toHaveLength(0)
  })

  it('should dismiss all toasts when no id provided', () => {
    const { result } = renderHook(() => useToast())
    
    act(() => {
      result.current.toast({ title: 'Toast 1' })
      result.current.toast({ title: 'Toast 2' })
    })

    expect(result.current.toasts).toHaveLength(1) // Due to limit

    act(() => {
      result.current.dismiss()
    })

    expect(result.current.toasts).toHaveLength(0)
  })

  it('should update an existing toast', () => {
    const { result } = renderHook(() => useToast())
    
    let toastId: string
    
    act(() => {
      result.current.toast({ title: 'Original Title' })
      toastId = result.current.toasts[0].id
    })

    act(() => {
      result.current.toast({
        id: toastId,
        title: 'Updated Title',
        description: 'Updated Description',
      })
    })

    expect(result.current.toasts).toHaveLength(1)
    expect(result.current.toasts[0]).toMatchObject({
      id: toastId,
      title: 'Updated Title',
      description: 'Updated Description',
    })
  })

  it('should handle toast with action', () => {
    const { result } = renderHook(() => useToast())
    
    const mockAction = {
      altText: 'Undo',
      onClick: jest.fn(),
    }
    
    act(() => {
      result.current.toast({
        title: 'Test Toast',
        action: mockAction,
      })
    })

    expect(result.current.toasts[0].action).toBe(mockAction)
  })

  it('should handle different toast variants', () => {
    const { result } = renderHook(() => useToast())
    
    act(() => {
      result.current.toast({
        title: 'Success Toast',
        variant: 'default',
      })
    })

    expect(result.current.toasts[0].variant).toBe('default')

    act(() => {
      result.current.toast({
        title: 'Error Toast',
        variant: 'destructive',
      })
    })

    expect(result.current.toasts[0].variant).toBe('destructive')
  })

  it('should generate unique ids for toasts', () => {
    const { result } = renderHook(() => useToast())
    
    const ids: string[] = []
    
    act(() => {
      for (let i = 0; i < 5; i++) {
        result.current.toast({ title: `Toast ${i}` })
        if (result.current.toasts.length > 0) {
          ids.push(result.current.toasts[0].id)
        }
      }
    })

    // All ids should be unique (though only last one remains due to limit)
    const uniqueIds = new Set(ids)
    expect(uniqueIds.size).toBe(ids.length)
  })

  it('should handle toast removal after timeout', (done) => {
    const { result } = renderHook(() => useToast())
    
    act(() => {
      result.current.toast({ title: 'Test Toast' })
    })

    expect(result.current.toasts).toHaveLength(1)

    // Since TOAST_REMOVE_DELAY is very long (1000000ms), we'll test the dismiss functionality instead
    const toastId = result.current.toasts[0].id

    act(() => {
      result.current.dismiss(toastId)
    })

    expect(result.current.toasts).toHaveLength(0)
    done()
  })

  it('should handle empty toast props', () => {
    const { result } = renderHook(() => useToast())
    
    act(() => {
      result.current.toast({})
    })

    expect(result.current.toasts).toHaveLength(1)
    expect(result.current.toasts[0].id).toBeDefined()
  })

  it('should not dismiss non-existent toast', () => {
    const { result } = renderHook(() => useToast())
    
    act(() => {
      result.current.toast({ title: 'Test Toast' })
    })

    expect(result.current.toasts).toHaveLength(1)

    act(() => {
      result.current.dismiss('non-existent-id')
    })

    // Toast should still be there
    expect(result.current.toasts).toHaveLength(1)
  })
})
