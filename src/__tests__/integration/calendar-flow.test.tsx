import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AppProvider, useCalendar, useAuth } from '@/contexts/AppContext'
import { useCalendarData } from '@/hooks/use-calendar-data'
import { mockCalendarData, mockUser } from '../mocks/data'

// Mock the calendar data hook
jest.mock('@/hooks/use-calendar-data')

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/calendar',
  }),
}))

// Create a test component that displays calendar data
const CalendarTestComponent = () => {
  const { calendar, student } = useCalendar()
  const { user, isAuthenticated } = useAuth()
  const { changeSemester, exportCalendar, logout } = useCalendarData()
  const [selectedSemester, setSelectedSemester] = React.useState('20231')

  const handleSemesterChange = async () => {
    await changeSemester(selectedSemester, {
      semesters: { currentSemester: '20231' },
      mainForm: { drpSemester: '20231' },
      signInToken: 'mock-token',
    })
  }

  const handleExport = () => {
    if (calendar && student) {
      exportCalendar(student, calendar)
    }
  }

  const handleLogout = () => {
    logout()
  }

  if (!isAuthenticated) {
    return <div data-testid="not-authenticated">Please login</div>
  }

  return (
    <div>
      <div data-testid="user-info">
        Welcome, {user?.name || 'Unknown User'}
      </div>
      
      <div data-testid="student-info">
        Student: {student || 'No student data'}
      </div>

      <div data-testid="semester-selector">
        <select
          value={selectedSemester}
          onChange={(e) => setSelectedSemester(e.target.value)}
          data-testid="semester-select"
        >
          <option value="20231">Semester 1 2023-2024</option>
          <option value="20232">Semester 2 2023-2024</option>
        </select>
        <button data-testid="change-semester-btn" onClick={handleSemesterChange}>
          Change Semester
        </button>
      </div>

      {calendar ? (
        <div data-testid="calendar-data">
          <div data-testid="semester-name">
            {calendar.semester.name}
          </div>
          <div data-testid="subjects-count">
            Subjects: {calendar.data_subject.length}
          </div>
          <div data-testid="subjects-list">
            {calendar.data_subject.map((subject, index) => (
              <div key={index} data-testid={`subject-${index}`}>
                {subject.name} - {subject.code}
              </div>
            ))}
          </div>
          <button data-testid="export-btn" onClick={handleExport}>
            Export Calendar
          </button>
        </div>
      ) : (
        <div data-testid="no-calendar">No calendar data</div>
      )}

      <button data-testid="logout-btn" onClick={handleLogout}>
        Logout
      </button>
    </div>
  )
}

describe('Calendar Flow Integration', () => {
  const mockChangeSemester = jest.fn()
  const mockExportCalendar = jest.fn()
  const mockLogout = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useCalendarData as jest.Mock).mockReturnValue({
      loginWithCredentials: jest.fn(),
      processManualData: jest.fn(),
      changeSemester: mockChangeSemester,
      exportCalendar: mockExportCalendar,
      logout: mockLogout,
      isProcessing: false,
    })
  })

  const renderWithProvider = (component: React.ReactElement) => {
    return render(
      <AppProvider>
        {component}
      </AppProvider>
    )
  }

  it('should display login prompt when not authenticated', () => {
    renderWithProvider(<CalendarTestComponent />)

    const notAuthMessage = screen.getByTestId('not-authenticated')
    expect(notAuthMessage).toHaveTextContent('Please login')
  })

  it('should display calendar data when authenticated', async () => {
    // Mock authenticated state with calendar data
    const TestWrapper = () => {
      const { dispatch } = require('@/contexts/AppContext').useApp()
      
      React.useEffect(() => {
        // Simulate login
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user: mockUser, signInToken: 'test-token' }
        })
        
        // Set calendar data
        dispatch({
          type: 'SET_CALENDAR',
          payload: mockCalendarData
        })
        
        // Set student data
        dispatch({
          type: 'SET_STUDENT',
          payload: 'Test Student - CT050101'
        })
      }, [dispatch])

      return <CalendarTestComponent />
    }

    renderWithProvider(<TestWrapper />)

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Welcome, Test User')
      expect(screen.getByTestId('student-info')).toHaveTextContent('Student: Test Student - CT050101')
      expect(screen.getByTestId('calendar-data')).toBeInTheDocument()
    })

    // Check calendar details
    expect(screen.getByTestId('semester-name')).toHaveTextContent('Học kỳ 1 năm 2023-2024')
    expect(screen.getByTestId('subjects-count')).toHaveTextContent('Subjects: 3')
    
    // Check subjects are displayed
    expect(screen.getByTestId('subject-0')).toHaveTextContent('Lập trình Web - IT4409')
    expect(screen.getByTestId('subject-1')).toHaveTextContent('Cơ sở dữ liệu - IT3090')
    expect(screen.getByTestId('subject-2')).toHaveTextContent('Mạng máy tính - IT4062')
  })

  it('should handle semester change', async () => {
    const user = userEvent.setup()
    mockChangeSemester.mockResolvedValue({ success: true })

    // Mock authenticated state
    const TestWrapper = () => {
      const { dispatch } = require('@/contexts/AppContext').useApp()
      
      React.useEffect(() => {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user: mockUser, signInToken: 'test-token' }
        })
        dispatch({
          type: 'SET_CALENDAR',
          payload: mockCalendarData
        })
      }, [dispatch])

      return <CalendarTestComponent />
    }

    renderWithProvider(<TestWrapper />)

    await waitFor(() => {
      expect(screen.getByTestId('semester-select')).toBeInTheDocument()
    })

    const semesterSelect = screen.getByTestId('semester-select')
    const changeSemesterBtn = screen.getByTestId('change-semester-btn')

    // Change semester selection
    await user.selectOptions(semesterSelect, '20232')
    expect(semesterSelect).toHaveValue('20232')

    // Click change semester button
    await user.click(changeSemesterBtn)

    await waitFor(() => {
      expect(mockChangeSemester).toHaveBeenCalledWith('20232', {
        semesters: { currentSemester: '20231' },
        mainForm: { drpSemester: '20231' },
        signInToken: 'mock-token',
      })
    })
  })

  it('should handle calendar export', async () => {
    const user = userEvent.setup()
    mockExportCalendar.mockReturnValue({ success: true })

    // Mock authenticated state with calendar
    const TestWrapper = () => {
      const { dispatch } = require('@/contexts/AppContext').useApp()
      
      React.useEffect(() => {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user: mockUser, signInToken: 'test-token' }
        })
        dispatch({
          type: 'SET_CALENDAR',
          payload: mockCalendarData
        })
        dispatch({
          type: 'SET_STUDENT',
          payload: 'Test Student'
        })
      }, [dispatch])

      return <CalendarTestComponent />
    }

    renderWithProvider(<TestWrapper />)

    await waitFor(() => {
      expect(screen.getByTestId('export-btn')).toBeInTheDocument()
    })

    const exportBtn = screen.getByTestId('export-btn')
    await user.click(exportBtn)

    expect(mockExportCalendar).toHaveBeenCalledWith('Test Student', mockCalendarData)
  })

  it('should handle logout', async () => {
    const user = userEvent.setup()

    // Mock authenticated state
    const TestWrapper = () => {
      const { dispatch } = require('@/contexts/AppContext').useApp()
      
      React.useEffect(() => {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user: mockUser, signInToken: 'test-token' }
        })
      }, [dispatch])

      return <CalendarTestComponent />
    }

    renderWithProvider(<TestWrapper />)

    await waitFor(() => {
      expect(screen.getByTestId('logout-btn')).toBeInTheDocument()
    })

    const logoutBtn = screen.getByTestId('logout-btn')
    await user.click(logoutBtn)

    expect(mockLogout).toHaveBeenCalledTimes(1)
  })

  it('should display no calendar message when no data available', async () => {
    // Mock authenticated state without calendar
    const TestWrapper = () => {
      const { dispatch } = require('@/contexts/AppContext').useApp()
      
      React.useEffect(() => {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user: mockUser, signInToken: 'test-token' }
        })
      }, [dispatch])

      return <CalendarTestComponent />
    }

    renderWithProvider(<TestWrapper />)

    await waitFor(() => {
      expect(screen.getByTestId('no-calendar')).toHaveTextContent('No calendar data')
    })
  })

  it('should disable export when no calendar or student data', async () => {
    // Mock authenticated state without complete data
    const TestWrapper = () => {
      const { dispatch } = require('@/contexts/AppContext').useApp()
      
      React.useEffect(() => {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user: mockUser, signInToken: 'test-token' }
        })
        // Only set calendar, no student data
        dispatch({
          type: 'SET_CALENDAR',
          payload: mockCalendarData
        })
      }, [dispatch])

      return <CalendarTestComponent />
    }

    renderWithProvider(<TestWrapper />)

    await waitFor(() => {
      expect(screen.getByTestId('export-btn')).toBeInTheDocument()
    })

    const user = userEvent.setup()
    const exportBtn = screen.getByTestId('export-btn')
    await user.click(exportBtn)

    // Should not call export without student data
    expect(mockExportCalendar).not.toHaveBeenCalled()
  })

  it('should maintain UI state during semester change', async () => {
    const user = userEvent.setup()
    mockChangeSemester.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({ success: true }), 100))
    )

    // Mock authenticated state
    const TestWrapper = () => {
      const { dispatch } = require('@/contexts/AppContext').useApp()
      
      React.useEffect(() => {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user: mockUser, signInToken: 'test-token' }
        })
        dispatch({
          type: 'SET_CALENDAR',
          payload: mockCalendarData
        })
      }, [dispatch])

      return <CalendarTestComponent />
    }

    renderWithProvider(<TestWrapper />)

    await waitFor(() => {
      expect(screen.getByTestId('semester-select')).toBeInTheDocument()
    })

    const semesterSelect = screen.getByTestId('semester-select')
    const changeSemesterBtn = screen.getByTestId('change-semester-btn')

    await user.selectOptions(semesterSelect, '20232')
    await user.click(changeSemesterBtn)

    // UI should remain responsive during the operation
    expect(screen.getByTestId('user-info')).toBeInTheDocument()
    expect(screen.getByTestId('calendar-data')).toBeInTheDocument()

    await waitFor(() => {
      expect(mockChangeSemester).toHaveBeenCalled()
    })
  })
})
